'use client';

import CookieConsent from 'react-cookie-consent';
import Link from 'next/link';

export default function CookiesConsentBanner() {
  return (
    <>
      <CookieConsent
        debug={false}
        style={{
          padding: '12px',
          background: '#59595990',
          textAlign: 'center',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          gap: '8px',
          fontSize: '12px',
          zIndex: '1111',
        }}
        buttonStyle={{
          background: '#000000',
          color: 'white',
          borderRadius: '6px',
          margin: '0px',
        }}
        buttonText="Ok"
        enableDeclineButton
        declineButtonText="×"
        declineButtonStyle={{
          background: 'transparent',
          alignSelf: 'anchor-center',
          color: '#ffffff',
          fontSize: '22px',
          padding: '0',
          margin: '0',
          position: 'absolute',
          right: '1rem',
        }}
        hideOnAccept={true}
      >
        We use cookies to improve your browsing experience. Learn about our{' '}
        <Link href="/privacy-policy/">Privacy policy</Link>.
      </CookieConsent>
    </>
  );
}
