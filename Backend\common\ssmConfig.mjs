import { SSMClient } from '@aws-sdk/client-ssm';

/**
 * SSM Parameter Store configuration service
 * Provides centralized configuration management with caching and fallback support
 */
class SSMConfigService {
  constructor() {
    this.ssmClient = null;
    this.config = null;
    this.lastFetchTime = 0;
    this.CACHE_TTL = 5 * 60 * 1000; // 5 minutes cache
    this.PARAMETER_PATH = '/maruti_site/env';
    this.isInitialized = false;

    // Initialize SSM client with default configuration
    // AWS SDK will automatically use environment variables, IAM roles, or AWS config
    this.ssmClient = new SSMClient({
      region: 'ap-south-1'
    });
  }

  /**
   * Initialize the configuration service
   * This should be called once during application startup
   */
  async initialize() {
    if (this.isInitialized) {
      return;
    }

    try {
      await this.fetchConfig();
      this.isInitialized = true;
      console.log('✅ SSM Configuration Service initialized successfully');
    } catch (error) {
      // Handle SSM error with fallback to environment variables
      try {
        console.warn('⚠️  SSM initialization failed, attempting environment variable fallback...');
        console.error('SSM Error:', error.message);
        this.loadFromEnvironment();
        this.isInitialized = true;
        console.log('🔄 SSM Configuration Service initialized with environment variable fallback');
      } catch (fallbackError) {
        console.error('Failed to initialize configuration from both SSM and environment variables:', fallbackError.message);
        throw new Error('Failed to initialize configuration from both SSM and environment variables');
      }
    }
  }

  /**
   * Get the current configuration
   * Automatically refreshes if cache has expired
   */
  async getConfig() {
    if (!this.isInitialized) {
      await this.initialize();
    }

    const now = Date.now();
    const cacheExpired = now - this.lastFetchTime > this.CACHE_TTL;

    if (!this.config || cacheExpired) {
      try {
        await this.fetchConfig();
      } catch (error) {
        console.error('Failed to refresh config from SSM, using cached/fallback values:', error);
        // If we have cached config, use it; otherwise load from environment
        if (!this.config) {
          this.loadFromEnvironment();
        }
      }
    }

    if (!this.config) {
      throw new Error('Configuration could not be loaded from SSM or environment variables');
    }

    return this.config;
  }

  /**
   * Fetch configuration from SSM Parameter Store (JSON format)
   */
  async fetchConfig() {
    try {
      // Use GetParameter instead of GetParametersByPath since we have a single JSON parameter
      const { GetParameterCommand } = await import('@aws-sdk/client-ssm');
      const command = new GetParameterCommand({
        Name: this.PARAMETER_PATH,
        WithDecryption: true
      });

      const response = await this.ssmClient.send(command);

      if (!response.Parameter || !response.Parameter.Value) {
        throw new Error(`No parameter found at path: ${this.PARAMETER_PATH}`);
      }

      // Parse the JSON configuration
      let jsonConfig;
      try {
        jsonConfig = JSON.parse(response.Parameter.Value);
      } catch (parseError) {
        throw new Error(`Failed to parse JSON configuration from SSM parameter: ${parseError}`);
      }

      // Convert JSON config to our AppConfig format
      const ssmConfig = {
        HUBSPOT_API_KEY: jsonConfig.NEXT_PUBLIC_HUBSPOT_API_KEY,
        HUBSPOT_GET_IN_TOUCH_FORM_GUID: jsonConfig.NEXT_PUBLIC_HUBSPOT_GET_IN_TOUCH_FORM_GUID,
        HUBSPOT_AI_READINESS_FORM_GUID: jsonConfig.NEXT_PUBLIC_HUBSPOT_AI_READINESS_FORM_GUID || jsonConfig.NEXT_PUBLIC_HUBSPOT_GET_IN_TOUCH_FORM_GUID, // Fallback to contact form GUID if AI readiness GUID is missing
        HUBSPOT_PORTAL_ID: jsonConfig.NEXT_PUBLIC_HUBSPOT_PORTAL_ID,
        MAIL_FROM: jsonConfig.NEXT_PUBLIC_MAIL_FROM,
        MAIL_TO: jsonConfig.NEXT_PUBLIC_MAIL_TO,
        SENDGRID_API_KEY: jsonConfig.NEXT_PUBLIC_SENDGRID_API_KEY,
        SENDGRID_CONTACT_US_FORM_TEMPLATE_ID: jsonConfig.NEXT_PUBLIC_SENDGRID_CONTACT_US_FORM_TEMPLATE_ID,
        SENDGRID_AI_READINESS_FORM_TEMPLATE_ID: jsonConfig.NEXT_PUBLIC_SENDGRID_AI_READINESS_FORM_TEMPLATE_ID || jsonConfig.NEXT_PUBLIC_SENDGRID_CONTACT_US_FORM_TEMPLATE_ID, // Fallback to contact template if AI readiness template is missing
        SENDGRID_FAILURE_EMAIL_TEMPLATE_ID: jsonConfig.NEXT_PUBLIC_SENDGRID_FAILURE_EMAIL_TEMPLATE_ID,
        SLACK_SUCCESS_WEBHOOK_URL: jsonConfig.NEXT_PUBLIC_SLACK_SUCCESS_WEBHOOK_URL,
        SLACK_FAILURE_WEBHOOK_URL: jsonConfig.NEXT_PUBLIC_SLACK_FAILURE_WEBHOOK_URL,
        SECOND_RECIPIENT: jsonConfig.NEXT_PUBLIC_SECOND_RECIPENT, // Note: keeping original typo for compatibility
        // Additional parameters that might be useful
        SITE_URL: jsonConfig.NEXT_PUBLIC_SITE_URL,
        STRAPI_URL: jsonConfig.NEXT_PUBLIC_STRAPI_URL
      };

      // Log any missing parameters for debugging
      const missingFromJson = [];
      if (!jsonConfig.NEXT_PUBLIC_HUBSPOT_AI_READINESS_FORM_GUID) {
        missingFromJson.push('NEXT_PUBLIC_HUBSPOT_AI_READINESS_FORM_GUID (using contact form GUID as fallback)');
      }
      if (!jsonConfig.NEXT_PUBLIC_SENDGRID_AI_READINESS_FORM_TEMPLATE_ID) {
        missingFromJson.push('NEXT_PUBLIC_SENDGRID_AI_READINESS_FORM_TEMPLATE_ID (using contact template as fallback)');
      }

      if (missingFromJson.length > 0) {
        console.warn('⚠️  Missing parameters in JSON config:', missingFromJson);
      }

      // Validate required parameters and merge with fallbacks
      this.config = this.validateAndMergeConfig(ssmConfig);
      this.lastFetchTime = Date.now();

      console.log(`✅ Successfully loaded configuration from SSM JSON parameter`);
      console.log(`📊 Available parameters: ${Object.keys(jsonConfig).length}`);
    } catch (error) {
      console.error(`Failed to fetch JSON configuration from SSM parameter: ${this.PARAMETER_PATH}`, error);
      throw new Error(`Failed to fetch JSON configuration from SSM parameter: ${this.PARAMETER_PATH}`);
    }
  }

  /**
   * Load configuration from environment variables as fallback
   */
  loadFromEnvironment() {
    console.log('Loading configuration from environment variables');

    this.config = this.validateAndMergeConfig({
      HUBSPOT_API_KEY: process.env.NEXT_PUBLIC_HUBSPOT_API_KEY,
      HUBSPOT_GET_IN_TOUCH_FORM_GUID: process.env.NEXT_PUBLIC_HUBSPOT_GET_IN_TOUCH_FORM_GUID,
      HUBSPOT_AI_READINESS_FORM_GUID: process.env.NEXT_PUBLIC_HUBSPOT_AI_READINESS_FORM_GUID,
      HUBSPOT_PORTAL_ID: process.env.NEXT_PUBLIC_HUBSPOT_PORTAL_ID,
      MAIL_FROM: process.env.NEXT_PUBLIC_MAIL_FROM,
      MAIL_TO: process.env.NEXT_PUBLIC_MAIL_TO,
      SENDGRID_API_KEY: process.env.NEXT_PUBLIC_SENDGRID_API_KEY,
      SENDGRID_CONTACT_US_FORM_TEMPLATE_ID: process.env.NEXT_PUBLIC_SENDGRID_CONTACT_US_FORM_TEMPLATE_ID,
      SENDGRID_AI_READINESS_FORM_TEMPLATE_ID: process.env.NEXT_PUBLIC_SENDGRID_AI_READINESS_FORM_TEMPLATE_ID,
      SENDGRID_FAILURE_EMAIL_TEMPLATE_ID: process.env.NEXT_PUBLIC_SENDGRID_FAILURE_EMAIL_TEMPLATE_ID,
      SLACK_SUCCESS_WEBHOOK_URL: process.env.NEXT_PUBLIC_SLACK_SUCCESS_WEBHOOK_URL,
      SLACK_FAILURE_WEBHOOK_URL: process.env.NEXT_PUBLIC_SLACK_FAILURE_WEBHOOK_URL,
      SECOND_RECIPIENT: process.env.NEXT_PUBLIC_SECOND_RECIPENT
    });

    this.lastFetchTime = Date.now();
  }

  /**
   * Validate and merge configuration with fallbacks
   */
  validateAndMergeConfig(ssmConfig) {
    const requiredParams = [
      'HUBSPOT_API_KEY',
      'HUBSPOT_GET_IN_TOUCH_FORM_GUID',
      'HUBSPOT_AI_READINESS_FORM_GUID',
      'HUBSPOT_PORTAL_ID',
      'MAIL_FROM',
      'MAIL_TO',
      'SENDGRID_API_KEY',
      'SENDGRID_CONTACT_US_FORM_TEMPLATE_ID',
      'SENDGRID_AI_READINESS_FORM_TEMPLATE_ID',
      'SENDGRID_FAILURE_EMAIL_TEMPLATE_ID',
      'SLACK_SUCCESS_WEBHOOK_URL',
      'SLACK_FAILURE_WEBHOOK_URL'
    ];

    // Parameters that can use fallbacks if missing
    const fallbackParams = [
      'HUBSPOT_AI_READINESS_FORM_GUID', // Can use contact form GUID
      'SENDGRID_AI_READINESS_FORM_TEMPLATE_ID' // Can use contact template
    ];

    const config = { ...ssmConfig };
    const missingParams = [];

    // Check required parameters and provide fallbacks
    for (const param of requiredParams) {
      if (!config[param]) {
        // Check if this parameter can use a fallback value
        let fallbackValue;

        if (fallbackParams.includes(param)) {
          if (param === 'HUBSPOT_AI_READINESS_FORM_GUID') {
            fallbackValue = config.HUBSPOT_GET_IN_TOUCH_FORM_GUID;
          } else if (param === 'SENDGRID_AI_READINESS_FORM_TEMPLATE_ID') {
            fallbackValue = config.SENDGRID_CONTACT_US_FORM_TEMPLATE_ID;
          }
        }

        if (fallbackValue) {
          config[param] = fallbackValue;
          console.log(`🔄 Using fallback value for ${param}`);
        } else {
          // Try to get from environment variable as fallback
          const envKey = `NEXT_PUBLIC_${param}`;
          const envValue = process.env[envKey];

          if (envValue) {
            config[param] = envValue;
            console.log(`🔄 Using environment variable fallback for ${param}`);
          } else {
            missingParams.push(param);
          }
        }
      }
    }

    if (missingParams.length > 0) {
      throw new Error(`Missing required configuration parameters: ${missingParams.join(', ')}`);
    }

    return config;
  }

  /**
   * Force refresh configuration from SSM
   */
  async refreshConfig() {
    this.lastFetchTime = 0; // Force cache expiry
    return await this.getConfig();
  }

  /**
   * Get a specific configuration value
   */
  async getConfigValue(key) {
    const config = await this.getConfig();
    return config[key];
  }

  /**
   * Check if the service is properly initialized
   */
  isServiceInitialized() {
    return this.isInitialized && this.config !== null;
  }
}

// Create and export a singleton instance
const ssmConfigService = new SSMConfigService();

export default ssmConfigService;

/**
 * Convenience function to get configuration
 * @returns Promise<AppConfig> The application configuration
 */
export const getConfig = async () => {
  return await ssmConfigService.getConfig();
};

/**
 * Convenience function to get a specific configuration value
 * @param key The configuration key to retrieve
 * @returns Promise<T> The configuration value
 */
export const getConfigValue = async (key) => {
  return await ssmConfigService.getConfigValue(key);
};
