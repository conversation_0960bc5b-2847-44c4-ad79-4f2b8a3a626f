/**
 * @fileoverview Global Unified Form Submission Handler for AWS Lambda
 * This AWS Lambda function handles multiple form submission types with a scalable architecture:
 * 1. Automatic form type detection using multiple strategies
 * 2. Modular form configuration system for easy extension
 * 3. Centralized processing logic with form-specific customizations
 * 4. Single entry point for all form submissions
 *
 * Supported form types:
 * - Contact Us forms
 * - AI Readiness Assessment forms
 * - Easily extensible for future form types
 *
 * Configuration is loaded from AWS SSM Parameter Store with fallback to environment variables.
 *
 * <AUTHOR> Team
 * @version 4.0.0
 */

import sendDataToHubspot from "../common/sendDataToHubSpot.mjs";
import sendDataToSendGrid from "../common/sendDataToSendGrid.mjs";
import currentTimestamp from "../common/currentTimestamp.mjs";
import sendToSlack from "../common/sendDataToSlack.mjs";
import { getConfigValue } from "../common/ssmConfig.mjs";

/**
 * Form configuration registry
 * Add new form types here with their specific configurations
 */
const FORM_CONFIGS = {
  "contact-us": {
    name: "Contact Us",
    hubspotFormGuidKey: "HUBSPOT_GET_IN_TOUCH_FORM_GUID",
    emailTemplateIdKey: "SENDGRID_CONTACT_US_FORM_TEMPLATE_ID",
    detectionRules: {
      pathKeywords: ["contact-us", "contact_us", "contact"],
      requiredFields: ["firstName", "lastName", "emailAddress"],
      uniqueFields: ["howCanWeHelpYou", "howDidYouHearAboutUs"],
      sourceKeywords: ["contact", "homepage", "getintouch"],
    },
    fieldMapping: (formData) => [
      { name: "firstname", value: formData?.firstName ?? "" },
      { name: "lastname", value: formData?.lastName ?? "" },
      { name: "email", value: formData?.emailAddress ?? "" },
      { name: "phone", value: formData?.phoneNumber ?? "" },
      {
        name: "how_did_you_hear_about_us_",
        value: formData?.howDidYouHearAboutUs ?? "",
      },
      { name: "company", value: formData?.companyName },
      { name: "message", value: formData?.howCanWeHelpYou },
      { name: "utm_source", value: formData?.utm_source ?? "" },
      { name: "utm_campaign", value: formData?.utm_campaign ?? "" },
      { name: "utm_medium", value: formData?.utm_medium ?? "" },
      { name: "clarity_link", value: formData?.clarity ?? "" },
      { name: "source_url", value: formData?.url ?? "" },
      { name: "referrer", value: formData?.referrer ?? "" },
      { name: "ip_address", value: formData?.ip_address ?? "" },
      { name: "city", value: formData?.city ?? "" },
      { name: "country", value: formData?.country ?? "" },
      { name: "ga_4_userid", value: formData?.ga_4_userid },
      { name: "source", value: formData?.secondary_source ?? "HomePage" },
      { name: "consent", value: formData?.consent ?? "" },
    ],
  },

  "ai-readiness": {
    name: "AI Readiness Assessment",
    hubspotFormGuidKey: "HUBSPOT_AI_READINESS_FORM_GUID",
    emailTemplateIdKey: "SENDGRID_AI_READINESS_FORM_TEMPLATE_ID",
    detectionRules: {
      pathKeywords: [
        "ai-readiness",
        "ai_readiness",
        "ai-assessment",
        "aireadiness",
      ],
      requiredFields: ["firstName", "lastName", "emailAddress"],
      uniqueFields: [
        "strategy___leadership",
        "talent___skills",
        "data_readiness___infrastructure",
        "impact_evaliation",
        "execution___monitoring",
        "average_of_all_score",
        "do_you_have_clearly_defined_business_objectives_and_goals_for_the_ai_project_",
      ],
      sourceKeywords: ["ai", "readiness", "assessment", "aireadiness"],
    },
    fieldMapping: (formData) => [
      { name: "firstname", value: formData?.firstName ?? "" },
      { name: "lastname", value: formData?.lastName ?? "" },
      { name: "email", value: formData?.emailAddress ?? "" },
      { name: "company", value: formData?.companyName },
      { name: "phone", value: formData?.phoneNumber ?? "" },
      { name: "city", value: formData?.city ?? "" },
      { name: "country", value: formData?.country ?? "" },
      { name: "ip_address", value: formData?.ip_address ?? "" },
      { name: "ga_4_userid", value: formData?.ga_4_userid },
      { name: "clarity_link", value: formData?.clarity ?? "" },
      { name: "source", value: formData?.secondary_source ?? "HomePage" },
      { name: "source_url", value: formData?.url ?? "" },
      { name: "utm_campaign", value: formData?.utm_campaign ?? "" },
      { name: "utm_source", value: formData?.utm_source ?? "" },
      { name: "utm_medium", value: formData?.utm_medium ?? "" },
      { name: "referrer", value: formData?.referrer ?? "" },
      { name: "consent", value: formData?.consent ?? "" },
      {
        name: "do_you_have_clearly_defined_business_objectives_and_goals_for_the_ai_project_",
        value:
          formData?.do_you_have_clearly_defined_business_objectives_and_goals_for_the_ai_project_ ??
          "",
      },
      {
        name: "how_receptive_is_your_leadership_team_to_embracing_the_changes_brought_about_by_ai_",
        value:
          formData?.how_receptive_is_your_leadership_team_to_embracing_the_changes_brought_about_by_ai_ ??
          "",
      },
      {
        name: "do_you_have_budget_allocated_for_your_ai_project_",
        value:
          formData?.do_you_have_budget_allocated_for_your_ai_project_ ?? "",
      },
      {
        name: "do_you_have_a_robust_data_infrastructure_for_storage__retrieval__and_processing_",
        value:
          formData?.do_you_have_a_robust_data_infrastructure_for_storage__retrieval__and_processing_ ??
          "",
      },
      {
        name: "which_of_the_below_db_tools_do_you_currently_use_",
        value:
          formData?.which_of_the_below_db_tools_do_you_currently_use_ ?? "",
      },
      {
        name: "is_the_relevant_data_for_the_ai_project_available_and_accessible_",
        value:
          formData?.is_the_relevant_data_for_the_ai_project_available_and_accessible_ ??
          "",
      },
      {
        name: "do_you_have_access_to_necessary_computing_resources__cpu__gpu__cloud_services__",
        value:
          formData?.do_you_have_access_to_necessary_computing_resources__cpu__gpu__cloud_services__ ??
          "",
      },
      {
        name: "how_would_you_rate_your_organization_s_current_it_infrastructure_in_terms_of_scalability_and_flexib",
        value:
          formData?.how_would_you_rate_your_organization_s_current_it_infrastructure_in_terms_of_scalability_and_flexib ??
          "",
      },
      {
        name: "does_the_team_have_the_expertise_in_data_science__machine_learning__and_ai_",
        value:
          formData?.does_the_team_have_the_expertise_in_data_science__machine_learning__and_ai_ ??
          "",
      },
      {
        name: "do_you_have_systems_in_place_to_monitor_the_performance_and_accuracy_of_ai_models_post_deployment_",
        value:
          formData?.do_you_have_systems_in_place_to_monitor_the_performance_and_accuracy_of_ai_models_post_deployment_ ??
          "",
      },
      {
        name: "do_you_have_risk_management_strategies_in_place_for_the_ai_project_",
        value:
          formData?.do_you_have_risk_management_strategies_in_place_for_the_ai_project_ ??
          "",
      },
      {
        name: "do_you_have_a_process_in_place_to_measure_the_impact_of_the_deployment_of_ai___ai_powered_solutions",
        value:
          formData?.do_you_have_a_process_in_place_to_measure_the_impact_of_the_deployment_of_ai___ai_powered_solutions ??
          "",
      },
      {
        name: "strategy___leadership",
        value: formData?.strategy___leadership ?? "",
      },
      { name: "talent___skills", value: formData?.talent___skills ?? "" },
      {
        name: "data_readiness___infrastructure",
        value: formData?.data_readiness___infrastructure ?? "",
      },
      { name: "impact_evaliation", value: formData?.impact_evaliation ?? "" },
      {
        name: "execution___monitoring",
        value: formData?.execution___monitoring ?? "",
      },
      {
        name: "average_of_all_score",
        value: formData?.average_of_all_score ?? "",
      },
    ],
  },

  // Future form types can be added here following the same pattern:
  // 'newsletter-signup': {
  //   name: 'Newsletter Signup',
  //   hubspotFormGuidKey: 'HUBSPOT_NEWSLETTER_FORM_GUID',
  //   emailTemplateIdKey: 'SENDGRID_NEWSLETTER_TEMPLATE_ID',
  //   detectionRules: { ... },
  //   fieldMapping: (formData) => [ ... ]
  // }
};

/**
 * Advanced form type detection using multiple strategies
 * @param {Object} event - AWS Lambda event object
 * @param {Object} formData - Parsed form data
 * @returns {string} Detected form type identifier
 */
function detectFormType(event, formData) {
  const scores = {};

  // Initialize scores for all form types
  Object.keys(FORM_CONFIGS).forEach((formType) => {
    scores[formType] = 0;
  });

  // Strategy 1: Path-based detection (highest priority)
  if (event.requestContext?.http?.path) {
    const path = event.requestContext.http.path.toLowerCase();
    Object.entries(FORM_CONFIGS).forEach(([formType, config]) => {
      config.detectionRules.pathKeywords.forEach((keyword) => {
        if (path.includes(keyword)) {
          scores[formType] += 100; // High score for path match
        }
      });
    });
  }

  // Strategy 2: Explicit form type in data (second highest priority)
  if (formData.formType) {
    const explicitType = formData.formType
      .toLowerCase()
      .replace(/[-_\s]/g, "-");
    if (FORM_CONFIGS[explicitType]) {
      scores[explicitType] += 90;
    }
  }

  // Strategy 3: Unique field detection (medium-high priority)
  Object.entries(FORM_CONFIGS).forEach(([formType, config]) => {
    config.detectionRules.uniqueFields.forEach((field) => {
      if (
        formData.hasOwnProperty(field) &&
        formData[field] !== undefined &&
        formData[field] !== "" &&
        formData[field] !== null
      ) {
        scores[formType] += 20; // Medium score for unique field match
      }
    });
  });

  // Strategy 4: Source-based detection (medium priority)
  if (formData.secondary_source) {
    const source = formData.secondary_source.toLowerCase();
    Object.entries(FORM_CONFIGS).forEach(([formType, config]) => {
      config.detectionRules.sourceKeywords.forEach((keyword) => {
        if (source.includes(keyword)) {
          scores[formType] += 15; // Medium score for source match
        }
      });
    });
  }

  // Strategy 5: Required field validation (low priority, for tie-breaking)
  Object.entries(FORM_CONFIGS).forEach(([formType, config]) => {
    const hasAllRequired = config.detectionRules.requiredFields.every(
      (field) =>
        formData.hasOwnProperty(field) &&
        formData[field] !== undefined &&
        formData[field] !== ""
    );
    if (hasAllRequired) {
      scores[formType] += 5; // Low score for having required fields
    }
  });

  // Find the form type with the highest score
  const detectedType = Object.entries(scores).reduce(
    (max, [formType, score]) => (score > max.score ? { formType, score } : max),
    { formType: "contact-us", score: 0 }
  );

  console.log("Form detection scores:", scores);
  console.log(
    `Detected form type: ${detectedType.formType} (score: ${detectedType.score})`
  );

  return detectedType.formType;
}

/**
 * Get form configuration by type
 * @param {string} formType - Form type identifier
 * @returns {Object} Form configuration object
 */
function getFormConfig(formType) {
  const config = FORM_CONFIGS[formType];
  if (!config) {
    console.warn(`Unknown form type: ${formType}, falling back to contact-us`);
    return FORM_CONFIGS["contact-us"];
  }
  return config;
}

/**
 * Build form fields using the form's field mapping function
 * @param {string} formType - Form type identifier
 * @param {Object} formData - Form data from request
 * @returns {Array} Array of form field objects
 */
function buildFormFields(formType, formData) {
  const config = getFormConfig(formType);
  return config.fieldMapping(formData);
}

/**
 * Process form submission with centralized logic
 * @param {string} formType - Type of form being processed
 * @param {Object} formData - Form data from request
 * @returns {Promise<Object>} Processing result
 */
async function processFormSubmission(formType, formData) {
  const config = getFormConfig(formType);
  const formFields = buildFormFields(formType, formData);

  console.log(`Processing ${config.name} form submission`);
  console.log(`Form fields count: ${formFields.length}`);

  try {
    // Get configuration values from SSM
    const [hubspotApiKey, hubspotFormGuid] = await Promise.all([
      getConfigValue("HUBSPOT_API_KEY"),
      getConfigValue(config.hubspotFormGuidKey),
    ]);

    const payload = {
      fields: formFields,
      context: { pageUri: formData?.url },
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${hubspotApiKey}`,
      },
    };

    // Send Data to HubSpot
    const hubspotResponse = await sendDataToHubspot(
      formData?.secondary_source,
      payload,
      hubspotFormGuid
    );

    if (hubspotResponse?.status === 200 || hubspotResponse?.status === 201) {
      // Success path - send confirmation email and Slack notification
      const [mailTo, mailFrom, emailTemplateId] = await Promise.all([
        getConfigValue("MAIL_TO"),
        getConfigValue("MAIL_FROM"),
        getConfigValue(config.emailTemplateIdKey),
      ]);

      const emailRes = await sendDataToSendGrid(
        mailTo,
        mailFrom,
        formData?.emailAddress,
        emailTemplateId,
        formData
      );

      await sendToSlack(formData);

      console.log(currentTimestamp());
      console.log("Lead Data", formData);
      console.log("Form Type", formType);
      console.log("Form Config", config.name);
      console.log("HubSpot Response", hubspotResponse);
      console.log("SendGrid Email Response", emailRes);
      console.log("------------------------------------");

      return {
        statusCode: 200,
        body: JSON.stringify({
          message: "Form submitted successfully.",
          formType: formType,
          formName: config.name,
          hubspotResponse: hubspotResponse.message,
        }),
      };
    } else {
      // Error path - send failure notifications
      return await handleFormSubmissionError(
        formType,
        formData,
        hubspotResponse,
        config
      );
    }
  } catch (error) {
    console.error(`Error processing ${config.name} form:`, error);
    return {
      statusCode: 500,
      body: JSON.stringify({
        message: `Internal server error while processing ${config.name} form`,
        formType: formType,
        formName: config.name,
        error: error.message || error,
      }),
    };
  }
}

/**
 * Handle form submission errors with proper notifications
 * @param {string} formType - Form type identifier
 * @param {Object} formData - Original form data
 * @param {Object} hubspotResponse - HubSpot error response
 * @param {Object} config - Form configuration
 * @returns {Promise<Object>} Error response
 */
async function handleFormSubmissionError(
  formType,
  formData,
  hubspotResponse,
  config
) {
  console.error(`${config.name} HubSpot Error:`, hubspotResponse);

  let formLeadData = { ...formData };
  formLeadData.page_name = formData?.secondary_source;
  formLeadData.failed_source = "Hubspot";
  formLeadData.form_type = formType;
  formLeadData.form_name = config.name;

  try {
    // Get failure email configuration from SSM
    const [failureMailTo, failureMailFrom, failureTemplateId] =
      await Promise.all([
        getConfigValue("MAIL_TO"),
        getConfigValue("MAIL_FROM"),
        getConfigValue("SENDGRID_FAILURE_EMAIL_TEMPLATE_ID"),
      ]);

    const failureEmail = await sendDataToSendGrid(
      failureMailTo,
      failureMailFrom,
      formData?.emailAddress,
      failureTemplateId,
      formLeadData
    );

    await sendToSlack(
      formData,
      undefined,
      `⚠️ ${config.name} Form Submission Failed ⚠️`
    );

    if (failureEmail.status) {
      console.error(`${config.name} form, failure email sent`);
    } else {
      console.error(`${config.name} form, failed to send failure email`);
    }
  } catch (notificationError) {
    console.error("Error sending failure notifications:", notificationError);
  }

  return {
    statusCode: hubspotResponse?.status || 500,
    body: JSON.stringify({
      message: `${config.name} form submission failed.`,
      formType: formType,
      formName: config.name,
      error: hubspotResponse?.error || "Unknown error from HubSpot",
    }),
  };
}

/**
 * Get list of all supported form types
 * @returns {Array} Array of form type identifiers
 */
function getSupportedFormTypes() {
  return Object.keys(FORM_CONFIGS);
}

/**
 * Validate form data against form type requirements
 * @param {string} formType - Form type identifier
 * @param {Object} formData - Form data to validate
 * @returns {Object} Validation result with isValid and errors
 */
function validateFormData(formType, formData) {
  const config = getFormConfig(formType);
  const errors = [];

  // Check required fields
  config.detectionRules.requiredFields.forEach((field) => {
    if (
      !formData.hasOwnProperty(field) ||
      formData[field] === undefined ||
      formData[field] === "" ||
      formData[field] === null
    ) {
      errors.push(`Missing required field: ${field}`);
    }
  });

  // Validate email format if present
  if (
    formData.emailAddress &&
    !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.emailAddress)
  ) {
    errors.push("Invalid email address format");
  }

  return {
    isValid: errors.length === 0,
    errors: errors,
  };
}

/**
 * AWS Lambda handler for global unified form submissions
 * Supports multiple form types with automatic detection and routing
 *
 * @async
 * @function handler
 * @param {Object} event - AWS Lambda event object
 * @param {string} event.body - JSON string containing form data
 * @param {Object} event.headers - HTTP headers from the request
 * @param {Object} event.requestContext - AWS Lambda request context
 * @returns {Promise<Object>} Lambda response object with statusCode and body
 *
 * @example
 * // Success response:
 * {
 *   "statusCode": 200,
 *   "body": "{\"message\":\"Form submitted successfully.\",\"formType\":\"contact-us\",\"formName\":\"Contact Us\",\"hubspotResponse\":\"Success\"}"
 * }
 *
 * @example
 * // Error response:
 * {
 *   "statusCode": 400,
 *   "body": "{\"message\":\"Form validation failed\",\"formType\":\"ai-readiness\",\"errors\":[\"Missing required field: firstName\"]}"
 * }
 */
export const handler = async (event) => {
  const startTime = Date.now();

  try {
    // Parse form data from request body
    const formData = JSON.parse(event.body);

    // Detect form type using advanced detection strategies
    const formType = detectFormType(event, formData);
    const config = getFormConfig(formType);

    console.log(`🚀 Processing ${config.name} form submission`);
    console.log(
      `📊 Supported form types: ${getSupportedFormTypes().join(", ")}`
    );
    console.log(
      `📝 Request path: ${event.requestContext?.http?.path || "N/A"}`
    );
    console.log(`🔍 Detected form type: ${formType}`);

    // Validate form data
    const validation = validateFormData(formType, formData);
    if (!validation.isValid) {
      console.error(
        `❌ Form validation failed for ${config.name}:`,
        validation.errors
      );
      return {
        statusCode: 400,
        body: JSON.stringify({
          message: "Form validation failed",
          formType: formType,
          formName: config.name,
          errors: validation.errors,
        }),
      };
    }

    // Process the form submission
    const result = await processFormSubmission(formType, formData);

    const processingTime = Date.now() - startTime;
    console.log(`⏱️ Processing completed in ${processingTime}ms`);

    return result;
  } catch (error) {
    const processingTime = Date.now() - startTime;
    console.error(`❌ Error parsing request (${processingTime}ms):`, error);

    return {
      statusCode: 400,
      body: JSON.stringify({
        message: "Invalid request data",
        error: error.message || error,
        supportedFormTypes: getSupportedFormTypes(),
      }),
    };
  }
};
