{
  "env": {
    "browser": true, // Specifies that the code will run in a browser environment
    "es2021": true // Specifies ES2021 as the target ECMAScript version
  },
  "extends": [
    "eslint:recommended", // Extends eslint recommended rules
    "plugin:@typescript-eslint/recommended", // Extends TypeScript ESLint recommended rules
    "plugin:react/recommended", // Extends React ESLint recommended rules
    "plugin:import/errors", // Extends import plugin error rules
    "plugin:import/warnings", // Extends import plugin warning rules
    "plugin:jsx-a11y/recommended", // Extends JSX accessibility ESLint recommended rules
    "plugin:react-hooks/recommended", // Extends React Hooks ESLint recommended rules
    "airbnb", // Extends Airbnb JavaScript style guide
    "prettier", // Extends Prettier rules
    "plugin:storybook/recommended" // Extends Storybook ESLint recommended rules
  ],
  "parser": "@typescript-eslint/parser", // Specifies TypeScript parser
  "parserOptions": {
    "ecmaFeatures": {
      "jsx": true // Enables JSX parsing
    },
    "ecmaVersion": "latest", // Specifies the latest ECMAScript version
    "sourceType": "module" // Specifies module code
  },
  "settings": {
    "react": {
      "version": "detect" // Automatically detects React version
    },
    "import/resolver": {
      "node": {
        "extensions": [".ts", ".tsx",".js",".jsx"],
        "moduleDirectory": ["node_modules", "src/"]
      },
      "alias": {
        // Specifies module aliases for import resolution
        "map": [
          ["@components", "./src/components"],
          ["@libtypes", "./src/types"],
          ["@hooks", "./src/hooks"],
          ["@constants", "./src/constants"],
          ["@utils", "./src/utils"],
          ["@styles", "./src/styles"],
          ["@stories", "./src/stories"],
          ["@contexts", "./src/contexts"],
          ["@public", "./src/public"]
        ],
        "extensions": [".ts", ".tsx",".js",".jsx"] // Specifies file extensions for import resolution
      }
    },
    "typescript": {} // Loads tsconfig.json to ESLint
  },
  "plugins": [
    "@typescript-eslint",
    "react",
    "simple-import-sort",
    "css-modules"
  ], // Specifies ESLint plugins
  "rules": {
    "react/prefer-stateless-function": "error", // Enforces preferring stateless functional components in React
    "react/jsx-filename-extension": [
      // Enforces JSX file extension
      "error",
      {
        "extensions": [".tsx"]
      }
    ],
    "react/jsx-pascal-case": "error", // Enforces PascalCase for JSX components
    "react/no-typos": "error", // Prevents common typos in React component names
    "react/no-array-index-key": "off", // Enforces providing unique keys for array elements
    "react/require-default-props": "off", // Disables requiring default props
    "react/jsx-uses-react": "off", // Disables requiring React import in JSX files
    "react/react-in-jsx-scope": "off", // Disables requiring React import in JSX files
    "no-restricted-exports": "off", // Disables restricting exports
    "import/no-extraneous-dependencies": ["error", { "devDependencies": true }], // Allows dev dependencies for imports
    "@typescript-eslint/naming-convention": [
      // Specifies naming conventions for TypeScript
      "error",
      {
        "selector": "default",
        "format": ["camelCase", "PascalCase"],
        "leadingUnderscore": "allow"
      },
      {
        "selector": "variable",
        "format": ["camelCase", "PascalCase", "UPPER_CASE"],
        "leadingUnderscore": "allow"
      },
      {
        "selector": "parameter",
        "format": ["camelCase"],
        "leadingUnderscore": "allow"
      },
      {
        "selector": "property",
        "format": null,
        "leadingUnderscore": "allow"
      },
      {
        "selector": "typeLike",
        "format": ["PascalCase"]
      }
    ],
    "simple-import-sort/imports": ["error"],
    "no-underscore-dangle": 0, // Allows underscores in identifiers
    "import/extensions": [
      // Specifies import extensions
      0,
      "never",
      {
        "js": "never",
        "jsx": "never",
        "ts": "never",
        "tsx": "never"
      }
    ],
    "no-console": "warn"
  },
  "overrides": [
    {
      "files": ["**/*.{ts,tsx,js,jsx}"],
      "rules": {
        "simple-import-sort/imports": [
          "error",
          {
            "groups": [
              // Group third-party imports starting with @ except specific patterns
              [
                "^react",
                "^next",
                "^[a-z]",
                "^@(?!components|contexts|libtypes|hooks|constants|utils|stories|styles)",
                "^@components",
                "^@contexts",
                "^@libtypes",
                "^@constants",
                "^@hooks",
                "^@utils",
                "^@stories",
                "^@styles"
              ],
              // Imports starting with `../` , `./`
              [
                "^\\.\\.(?!/?$)",
                "^\\.\\./?$",
                "^\\./(?=.*/)(?!/?$)",
                "^\\.(?!/?$)",
                "^\\./?$"
              ]
            ]
          }
        ]
      }
    }
  ]
}