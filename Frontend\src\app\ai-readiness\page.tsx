import { notFound } from 'next/navigation';
import AIReadinessBody from '@components/AIReadinessBody';
import seoSchema from '@utils/seoSchema';

import fetchFromStrapi from '@utils/fetchFromStrapi';
import RichResults from '@components/RichResults';

async function getAIReadinessData() {
  return await fetchFromStrapi(
    'ai-readiness',
    'populate=hero_section.image,ai_readiness_components.question.answers,ai_readiness_components.question.sub_question,form.formFields,form.button,restart_button,consultation_button,tag_list,tag,seo.schema',
  );
}

async function getFormData() {
  const query = `populate=form.formFields&populate=form.button`;
  return await fetchFromStrapi('form', query);
}

export async function generateMetadata({}) {
  const seoFetchedData = await fetchFromStrapi(
    'ai-readiness',
    'populate=seo&populate=seo.image&populate=seo.metaProperties&populate=seo.keywords,seo.schema',
  );
  const seoData = seoFetchedData?.data?.attributes?.seo;

  return seoSchema(seoData);
}

export default async function AboutUs() {
  const AIReadinessData = await getAIReadinessData();
  const formData = await getFormData();

  if (!AIReadinessData?.data || AIReadinessData?.data.length === 0) {
    notFound();
  }

  return (
    <>
      {AIReadinessData?.data?.attributes?.seo && (
        <RichResults data={AIReadinessData?.data?.attributes?.seo} />
      )}
      {AIReadinessData?.data?.attributes && (
        <AIReadinessBody
          body={AIReadinessData?.data?.attributes}
          formData={formData?.data?.attributes?.form}
        />
      )}
    </>
  );
}
