# Global Unified Form Handler

## Overview

This is a scalable, global solution for handling multiple form submission types in a single AWS Lambda function. The architecture is designed to easily accommodate future form types while maintaining clean separation of concerns and robust error handling.

## 🏗️ Architecture

### Core Components

1. **Form Configuration Registry** (`FORM_CONFIGS`)
   - Centralized configuration for all form types
   - Easy to extend for new forms
   - Contains detection rules, field mappings, and service configurations

2. **Advanced Form Detection** (`detectFormType`)
   - Multi-strategy detection with scoring system
   - Path-based, field-based, and explicit type detection
   - Fallback mechanisms for edge cases

3. **Unified Processing Pipeline** (`processFormSubmission`)
   - Centralized logic for all form types
   - Consistent error handling and logging
   - Shared integrations (HubSpot, SendGrid, Slack)

4. **Validation System** (`validateFormData`)
   - Form-specific validation rules
   - Email format validation
   - Required field checking

## 📋 Currently Supported Form Types

### 1. Contact Us Forms (`contact-us`)
- **Detection**: Path keywords, unique fields like `howCanWeHelpYou`
- **HubSpot**: Uses `HUBSPOT_GET_IN_TOUCH_FORM_GUID`
- **Email**: Uses `SENDGRID_CONTACT_US_FORM_TEMPLATE_ID`

### 2. AI Readiness Assessment (`ai-readiness`)
- **Detection**: Path keywords, AI-specific fields like `strategy___leadership`
- **HubSpot**: Uses `HUBSPOT_AI_READINESS_FORM_GUID`
- **Email**: Uses `SENDGRID_AI_READINESS_FORM_TEMPLATE_ID`

## 🔍 Form Detection Strategies

The system uses a sophisticated scoring mechanism to determine form type:

### 1. Path-Based Detection (Score: 100)
- Highest priority
- Checks URL path for keywords: `contact-us`, `ai-readiness`, etc.

### 2. Explicit Form Type (Score: 90)
- Second highest priority
- Looks for `formType` field in form data

### 3. Unique Field Detection (Score: 20 per field)
- Analyzes form fields for type-specific indicators
- AI forms: `strategy___leadership`, `average_of_all_score`
- Contact forms: `howCanWeHelpYou`, `howDidYouHearAboutUs`

### 4. Source-Based Detection (Score: 15)
- Examines `secondary_source` field for keywords

### 5. Required Field Validation (Score: 5)
- Tie-breaker for edge cases
- Ensures form has minimum required fields

## 🚀 Adding New Form Types

Adding a new form type is straightforward:

```javascript
// Add to FORM_CONFIGS object
'newsletter-signup': {
  name: 'Newsletter Signup',
  hubspotFormGuidKey: 'HUBSPOT_NEWSLETTER_FORM_GUID',
  emailTemplateIdKey: 'SENDGRID_NEWSLETTER_TEMPLATE_ID',
  detectionRules: {
    pathKeywords: ['newsletter', 'subscribe'],
    requiredFields: ['emailAddress'],
    uniqueFields: ['subscriptionType', 'preferences'],
    sourceKeywords: ['newsletter', 'subscribe']
  },
  fieldMapping: (formData) => [
    { name: "email", value: formData?.emailAddress ?? "" },
    { name: "subscription_type", value: formData?.subscriptionType ?? "" },
    // ... other fields
  ]
}
```

### Steps to Add New Form:
1. Add configuration to `FORM_CONFIGS`
2. Add SSM parameters for HubSpot and email templates
3. Test with the test suite
4. Update API Gateway routes if needed

## 🧪 Testing

Run the comprehensive test suite:

```bash
cd Backend/api
node test-global-handler.mjs
```

### Test Coverage:
- ✅ Path-based form detection
- ✅ Field-based form detection  
- ✅ Explicit form type specification
- ✅ Validation error handling
- ✅ Edge cases (empty data, invalid JSON)
- ✅ Multiple form types simultaneously

## 📡 API Endpoints

All endpoints point to the same Lambda function with intelligent routing:

- `POST /contact-us` - Contact form submissions
- `POST /ai-readiness` - AI readiness assessments
- `POST /api/forms` - Generic endpoint (uses field detection)

## 🔧 Configuration

### Required SSM Parameters:
```
/maruti_site/env (JSON format):
{
  "NEXT_PUBLIC_HUBSPOT_API_KEY": "...",
  "NEXT_PUBLIC_HUBSPOT_GET_IN_TOUCH_FORM_GUID": "...",
  "NEXT_PUBLIC_HUBSPOT_AI_READINESS_FORM_GUID": "...",
  "NEXT_PUBLIC_SENDGRID_API_KEY": "...",
  "NEXT_PUBLIC_SENDGRID_CONTACT_US_FORM_TEMPLATE_ID": "...",
  "NEXT_PUBLIC_SENDGRID_AI_READINESS_FORM_TEMPLATE_ID": "...",
  "NEXT_PUBLIC_SENDGRID_FAILURE_EMAIL_TEMPLATE_ID": "...",
  "NEXT_PUBLIC_SLACK_SUCCESS_WEBHOOK_URL": "...",
  "NEXT_PUBLIC_SLACK_FAILURE_WEBHOOK_URL": "...",
  "NEXT_PUBLIC_MAIL_TO": "...",
  "NEXT_PUBLIC_MAIL_FROM": "..."
}
```

## 📊 Monitoring & Logging

The handler provides comprehensive logging:

- 🚀 Form processing start with timing
- 📊 Supported form types list
- 📝 Request path and detection details
- 🔍 Form type detection scores
- ⏱️ Processing time measurement
- ❌ Detailed error logging with context

### Log Example:
```
🚀 Processing Contact Us form submission
📊 Supported form types: contact-us, ai-readiness
📝 Request path: /contact-us
🔍 Detected form type: contact-us
Form detection scores: {"contact-us": 100, "ai-readiness": 0}
⏱️ Processing completed in 1250ms
```

## 🔄 Migration from Individual Handlers

### Before:
```
Backend/api/
├── contact-us/index.mjs
└── ai-readiness/index.mjs
```

### After:
```
Backend/api/
├── index.mjs (unified handler)
├── test-global-handler.mjs
└── README-GLOBAL-HANDLER.md
```

### Terraform Changes:
- Handler: `api/contact-us/index.handler` → `api/index.handler`
- Routes: Both `/contact-us` and `/ai-readiness` point to same function

## 🎯 Benefits

1. **Scalability**: Easy to add new form types
2. **Maintainability**: Single codebase for all forms
3. **Consistency**: Unified error handling and logging
4. **Flexibility**: Multiple detection strategies
5. **Robustness**: Comprehensive validation and fallbacks
6. **Performance**: Shared resources and optimized processing
7. **Monitoring**: Rich logging and metrics

## 🚨 Error Handling

The system handles various error scenarios:

- **Validation Errors**: Missing fields, invalid email format
- **HubSpot Failures**: Automatic retry and failure notifications
- **SendGrid Issues**: Graceful degradation with logging
- **Slack Notifications**: Non-blocking error reporting
- **JSON Parsing**: Proper error responses for malformed data

## 🔮 Future Enhancements

Potential improvements for the global handler:

1. **Rate Limiting**: Per-form-type rate limiting
2. **Caching**: Configuration and template caching
3. **Metrics**: CloudWatch custom metrics
4. **A/B Testing**: Form variant support
5. **Webhooks**: Generic webhook support for integrations
6. **File Uploads**: Support for file attachments
7. **Multi-language**: Internationalization support

---

This global solution provides a robust, scalable foundation for handling all current and future form submission requirements while maintaining clean architecture and excellent developer experience.
