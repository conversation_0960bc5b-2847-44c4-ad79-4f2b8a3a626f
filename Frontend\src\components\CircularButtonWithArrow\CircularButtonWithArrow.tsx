'use client';
import { useEffect, useState } from 'react';
import Image from 'next/image';
import styles from './CircularButtonWithArrow.module.css';

export default function CircularButtonWithArrow({
  variant = 'large',
  scroll_to = 'false',
}: any) {
  const [isVisible, setIsVisible] = useState(false); // State to control visibility

  if (scroll_to === true) {
    useEffect(() => {
      const handleScroll = () => {
        const scrollTop = window.scrollY;
        if (scrollTop > 600) {
          setIsVisible(true);
        } else {
          setIsVisible(false);
        }
      };

      window.addEventListener('scroll', handleScroll);
      return () => {
        window.removeEventListener('scroll', handleScroll);
      };
    }, []);
  }

  const imageSizes = {
    large: {
      circle: {
        url: 'https://cdn.marutitech.com/circle_large_5d770e7963.svg',
        width: 98,
        height: 98,
      },
      arrow: {
        url: 'https://cdn.marutitech.com/arrow_large_4c0720e1c9.svg',
        width: 53,
        height: 53,
      },
    },
    medium: {
      circle: {
        url: 'https://cdn.marutitech.com/circle_medium_cc9c77e620.svg',
        width: 77,
        height: 77,
      },
      arrow: {
        url: 'https://cdn.marutitech.com/arrow_medium_2fd9472b7f.svg',
        width: 43,
        height: 43,
      },
    },
    small: {
      circle: {
        url: 'https://cdn.marutitech.com/circle_small_b122f67035.svg',
        width: 52,
        height: 52,
      },
      arrow: {
        url: 'https://cdn.marutitech.com/arrow_small_113c2e8618.svg',
        width: 30,
        height: 30,
      },
    },
    scroll_to_top: {
      circle: {
        url: 'https://cdn.marutitech.com/Ellipse_2_Stroke_c048e39778.svg',
        width: 60,
        height: 60,
      },
      arrow: {
        url: 'https://cdn.marutitech.com/Group_5_b251b9ec9d.svg',
        width: 35,
        height: 34,
      },
    },
  };
  const { circle, arrow } = imageSizes[variant];
  // Function to handle scrolling to the top
  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth',
    });
  };

  return (
    <>
      {scroll_to && isVisible && (
        <div
          className={styles.container}
          style={{ width: circle.width, height: circle.height }}
          onClick={scrollToTop}
        >
          <div className={styles.circle}>
            <Image
              src={circle.url}
              alt="Circle"
              width={circle.width}
              height={circle.height}
            />
          </div>
          <div
            className={styles.arrow_scroll}
            style={{ width: arrow.width, height: arrow.height }}
          >
            <Image
              src={arrow.url}
              alt="Arrow"
              width={arrow.width}
              height={arrow.height}
              className={styles.arrowImage}
            />
          </div>
        </div>
      )}
      {scroll_to === 'false' && (
        <div
          className={styles.container}
          style={{ width: circle.width, height: circle.height }}
        >
          <div className={styles.circle}>
            <Image
              src={circle.url}
              alt="Circle"
              width={circle.width}
              height={circle.height}
            />
          </div>
          <div
            className={styles.arrow}
            style={{ width: arrow.width, height: arrow.height }}
          >
            <Image
              src={arrow.url}
              alt="Arrow"
              width={arrow.width}
              height={arrow.height}
              className={styles.arrowImage}
            />
          </div>
        </div>
      )}
    </>
  );
}
