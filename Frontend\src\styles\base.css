/* normalize styling across browsers */
html {
  width: 100%;
  height: 100%;
}

html {
  box-sizing: border-box;
}

* {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

h1,
h2,
h3,
h4,
h5,
h6,
p,
blockquote,
figure,
ol,
ul {
  margin: 0;
  padding: 0;
}

ul,
ol {
  padding-left: 42px;
}

h1,
h2,
h3,
h4,
h5,
h6,
a {
  white-space: pre-line;

  color: inherit;
}

p {
  margin-bottom: 1rem;
}

body {
  width: 100%;
  margin: 0;
  padding: 0 !important;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  position: relative;
}

button {
  border: none;
  outline: none;
  background: unset;
}

.navbar {
  --bs-navbar-padding-y: 1.5rem;
}

.navbar-nav .nav-link.active {
  color: white;
}

@media (min-width: 576px) {
  .container-sm,
  .container {
    max-width: 576px;
  }
}

@media (min-width: 768px) {
  .container-md,
  .container-sm,
  .container {
    max-width: 704px;
  }
}

@media (min-width: 992px) {
  .container-lg,
  .container-md,
  .container-sm,
  .container {
    max-width: 992px;
  }
}

@media (min-width: 1200px) {
  .container-xl,
  .container-lg,
  .container-md,
  .container-sm,
  .container {
    max-width: 1200px;
  }
}

@media (min-width: 1400px) {
  .container-xxl,
  .container-xl,
  .container-lg,
  .container-md,
  .container-sm,
  .container {
    max-width: 1238px;
  }
}

@media (min-width: 2560px) {
  .container-xxl,
  .container-xl,
  .container-lg,
  .container-md,
  .container-sm,
  .container {
    max-width: 2260px;
  }
}

.row {
  margin-top: 0;
  margin-right: 0;
}

.accordion {
  --bs-accordion-bg: black;
  --bs-accordion-color: white;
  --bs-accordion-border-radius: 0px;
  --bs-accordion-border-color: transparent;
  --bs-accordion-active-color: transparent;
  --bs-accordion-active-bg: transparent;
}

.accordion-button {
  color: white;
  font-size: 20px;
  border-bottom: 1px solid gray;
  padding: 15px 0px;
}

.accordion-button:focus {
  z-index: 3;
  outline: 0;
  box-shadow: none;
}

.accordion-button::after {
  -webkit-filter: grayscale(1) invert(1);
  filter: grayscale(1) invert(1);
  height: 10px;
  width: 10px;
  background-size: auto;
  margin-left: 5px;
}

.accordion-item:first-of-type {
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}

.accordion-item:last-of-type > .accordion-header .accordion-button.collapsed {
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
}

.accordion-button:not(.collapsed) {
  color: white;
  background-color: transparent;
}

.modal-content {
  background-color: transparent;
  border: 0;
  border-radius: 0;
  outline: 0;
}

.modal-header .btn-close {
  /* position: fixed !important; */
  opacity: 1;
  font-weight: bold;
  font-size: 22px;
  height: 4px;
  width: 3px;
  color: #e7e7e7 !important;
  /* right: 2.8% !important; */
  /* top: 4% !important; */
  background: transparent
    url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23f6f6f6'%3e%3cpath d='M.293.293a1 1 0 011.414 0L8 6.586 14.293.293a1 1 0 111.414 1.414L9.414 8l6.293 6.293a1 1 0 01-1.414 1.414L8 9.414l-6.293 6.293a1 1 0 01-1.414-1.414L6.586 8 .293 1.707a1 1 0 010-1.414z'/%3e%3c/svg%3e")
    center/1em auto no-repeat;
}

.btn-close:focus {
  box-shadow: none;
}

/* to change the plus and minus icon of accordion */
.accordionIcon > button::after {
  background-image: url('../public/plus.svg');
  background-position: center;
  background-size: 24px;
  height: 24px;
  width: 24px;
}

.accordionIcon > button:not(.collapsed)::after {
  background-image: url('../public/minus.svg');
  background-position: center;
  background-size: 24px;
  height: 24px;
  width: 24px;
}

.hidden {
  visibility: hidden;
  height: 0;
  width: 0;
  margin: 0;
  padding: 0;
}

.ais-Hits-list {
  list-style: none !important;
  padding: 0;
}

.CookieConsent {
  @media only screen and (max-width: 768px) {
    padding: 12px 16px !important;
  }

  a {
    text-decoration: underline !important;
  }
}

.CookieConsent > div {
  flex: none !important;
  margin: 0px !important;

  @media only screen and (max-width: 556px) {
    max-width: 80vw !important;
  }
}

#rcc-decline-button {
  @media only screen and (max-width: 768px) {
    top: 0px !important;
    right: 5px !important;
  }
}

/* Scroll to top button */
#scroll_to_top {
  position: fixed;
  right: 16px;
  bottom: 68px;
  z-index: 1000;
}

/* Form -> Contact number -> React phone input-2 library -> Selected flag hover */
.selected-flag {
  background-color: #202020 !important;
}

.selected-flag.open,
.selected-flag:hover {
  background-color: #646464 !important;
}

/* Form -> Contact number -> React phone input-2 library  -> Selected flag -> Arrow */
.arrow {
  border-top: 5px solid #fff !important;
}

.blur {
  filter: blur(10px);
  box-shadow: 0 0 40px 40px black !important;
}

figure > img {
  width: 100%;
}
