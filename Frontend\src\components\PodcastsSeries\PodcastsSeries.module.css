@value variables: "@styles/variables.module.css";
@value colorBlack, colorWhite, gray600 from variables;
@value breakpoints: "@styles/breakpoints.module.css";
@value breakpoint-sm, breakpoint-xl, breakpoint-xl-1024, breakpoint-xl-1440 from breakpoints;

.podcast_series_container {
  margin: 0;
  padding: 0;
}

.podcast_series {
  padding: 5rem 9.375rem;
  display: flex;
  flex-direction: column;
  gap: 40px;
  align-items: center;

  @media (max-width: breakpoint-xl-1440) {
    padding: 5rem 7.75rem;
  }

  @media (max-width: breakpoint-xl) {
    padding: 5rem 2rem;
  }

  @media (max-width: breakpoint-sm) {
    padding: 2.5rem 1rem;
  }
}

.podcast_variant_black {
  color: colorWhite !important;
  background-color: colorBlack !important;
}

.podcast_series_title > h2 {
  text-align: center;
  font-size: 40px;
  font-weight: 600;
  line-height: 140%; /* 56px */
  letter-spacing: -0.8px;
}

.episode_tabs {
  width: 100%;
  user-select: none;
  display: flex;
  justify-content: center;
  gap: 20px;
  flex-wrap: wrap;

  @media (max-width: breakpoint-xl-1024) {
    padding-bottom: 10px;
    overflow-x: auto;
    overflow-y: hidden;
    flex-wrap: nowrap;
    justify-content: flex-start;
  }
}

.episode_tabs::-webkit-scrollbar {
  height: 5px !important;
}

.episode_tabs::-webkit-scrollbar-thumb {
  background-color: gray600;
  border-radius: 8px;
}

.tab_button_varient_black {
  padding: 6px 10px;
  border-radius: 6px;
  border: 1px solid colorWhite;
  background: colorBlack;
  color: colorWhite;
}

.tab_button_active_varient_black {
  background: colorWhite;
  color: colorBlack;
}

.tab_button_varient_white {
  padding: 6px 10px;
  border-radius: 8px;
  border: 1px solid colorBlack;
  background: colorWhite;
}

.tab_button_active_varient_white {
  background: colorBlack;
  color: colorWhite;
}

.episode_content {
  display: none;
}

.episode_content_active {
  display: block;
}

.content_wrapper {
  max-width: 1200px;
  display: flex;
  gap: 40px;
  align-items: center;
  justify-content: center;

  @media (max-width: breakpoint-xl-1024) {
    flex-direction: column;
    gap: 20px;
  }
}

.flex_row_reverse {
  flex-direction: row-reverse;

  @media (max-width: breakpoint-xl-1024) {
    flex-direction: column;
  }
}

.video_section {
  position: relative;
  width: 50%;
  height: 556px;

  @media (max-width: breakpoint-xl-1024) {
    max-width: 100%;
    width: 100%;
  }

  @media screen and (max-width: breakpoint-sm) {
    height: 350px;
  }
}

.video_thumbnail {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.content_section {
  width: 50%;

  @media (max-width: breakpoint-xl-1024) {
    width: 100%;
  }
}

.episode_title > h3 {
  font-size: 40px;
  font-weight: 600;
  line-height: 140%;
  letter-spacing: -0.8px;
}

.episode_description {
  margin-top: 24px;
  font-size: 20px;
  font-weight: 400;
  line-height: 160%;
}

.audio_section {
  display: flex;
  gap: 12px;
  align-items: center;
}

.audio_player {
  width: 100%;
  height: 50px;
}

.spotify_link {
  font-size: 20px;
  font-weight: 400;
  line-height: 160%;
}
