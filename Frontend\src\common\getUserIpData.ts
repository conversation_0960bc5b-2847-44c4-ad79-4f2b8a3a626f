const getUserIPData = async () => {
  const storageKey = 'user_ip_location';

  // Step 1: Check localStorage
  const cachedData = localStorage.getItem(storageKey);
  if (cachedData) {
    try {
      return JSON.parse(cachedData);
    } catch (err) {
      console.warn('Failed to parse cached IP data:', err);
    }
  }

  // Step 2: If not in localStorage, fetch the data
  try {
    // Fetch IP address
    const ipResponse = await fetch('https://api.ipify.org?format=json');
    if (!ipResponse.ok) {
      throw new Error(`IP fetch error! Status: ${ipResponse.status}`);
    }

    const ipData = await ipResponse.json();
    const ipAddress = ipData.ip || '';

    // Fetch location for the IP
    const locationResponse = await fetch(`https://ipapi.co/${ipAddress}/json/`);
    if (!locationResponse.ok) {
      throw new Error(
        `Location fetch error! Status: ${locationResponse.status}`,
      );
    }

    const locationData = await locationResponse.json();
    const result = {
      ipAddress,
      location: {
        city: locationData.city || '',
        country: locationData.country_name || '',
        country_code: locationData.country_code || '',
      },
    };

    // Save to localStorage
    localStorage.setItem(storageKey, JSON.stringify(result));

    return result;
  } catch (error) {
    console.error('Error fetching IP or location:', error);
    return {
      ipAddress: '',
      location: { city: '', country: '', country_code: '' },
    };
  }
};

export default getUserIPData;
