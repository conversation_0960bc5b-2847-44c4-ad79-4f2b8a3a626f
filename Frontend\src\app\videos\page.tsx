import CaseStudyHeroSection from '@components/CaseStudyHeroSection';
import VideoListing from '@components/VideoListing';
import seoSchema from '@utils/seoSchema';
import RichResults from '@components/RichResults';
import fetchFromStrapi from '@utils/fetchFromStrapi';
import { Suspense } from 'react';

async function fetchDropDownVideoTagsData() {
  return await fetchFromStrapi('global-video-tags');
}

async function fetchVideosPageData() {
  const query = `populate=hero_section.image&populate=all_videos&populate=play_button_across_page,seo.schema`;
  return await fetchFromStrapi('video-page', query);
}

async function fetchVideos() {
  const query = `populate=thumbnail&populate=global_video_tag&sort=publication_date:desc`;
  return await fetchFromStrapi('videos', query);
}

export async function generateMetadata({}) {
  const query = `populate=seo&populate=seo.image&populate=seo.metaProperties&populate=seo.keywords,seo.schema`;
  const seoFetchedData = await fetchFromStrapi('video-page', query);

  const seoData = seoFetchedData?.data?.attributes?.seo;
  return seoSchema(seoData);
}

export default async function Videos() {
  const dropDownVideoTagsData = await fetchDropDownVideoTagsData();
  const videosPageData = await fetchVideosPageData();
  const videos = await fetchVideos();

  return (
    <>
      {videosPageData?.data?.attributes?.seo && (
        <RichResults data={videosPageData?.data?.attributes?.seo} />
      )}
      {videosPageData?.data?.attributes && (
        <CaseStudyHeroSection
          heroData={videosPageData?.data?.attributes?.hero_section}
          variant={'listing_page'}
        />
      )}
      <Suspense>
        <VideoListing
          filterdata={videosPageData?.data?.attributes}
          boxData={videos?.data}
          dropDownVideoTagsData={dropDownVideoTagsData?.data}
        />
      </Suspense>
    </>
  );
}
