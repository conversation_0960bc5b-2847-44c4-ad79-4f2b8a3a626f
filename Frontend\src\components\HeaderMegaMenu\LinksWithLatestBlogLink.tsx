'use client';

/* eslint-disable react/no-danger */
import React, { useEffect, useState } from 'react';
import { Col, Row } from 'react-bootstrap';
import Image from 'next/image';
import ArrowRight from '@public/arrow-right.svg';
import styles from '@components/Header/Header.module.css';
import Link from '@components/Link/Link';
import classNames from '@utils/classNames';

import MenuLink from './MenuLink';

export default function LinksWithLatestBlogLink({
  links,
  button,
  titleDescription,
  onClick,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
}: any) {
  const [isRendered, setIsRendered] = useState(false);
  useEffect(() => {
    setIsRendered(true);
  }, []);

  if (!isRendered) {
    return null;
  }
  return (
    <>
      <div className={styles.megaMenuContent}>
        <Row>
          <Col>
            <div className={styles.menuWrapper}>
              <Row>
                {links?.map(link => (
                  <Col
                    className={classNames(
                      'col-sm-12 col-md-12 col-lg-4 col-xl-4',
                      styles.flexDirectionColumn,
                    )}
                    key={link?.id}
                  >
                    <MenuLink
                      linkTitle={link?.title}
                      href={link.link}
                      onClick={onClick}
                    />
                  </Col>
                ))}
              </Row>
            </div>
          </Col>
          <Col
            className={classNames(
              'col-sm-12 col-md-12 col-lg-4 col-xl-3',
              styles.flexDirectionColumn,
            )}
          >
            <div className={styles.menuWrapper}>
              <div
                className={classNames(styles.latestBlogWrapper, styles.link)}
              >
                <p className={styles.blogHeading}>{titleDescription?.title}</p>

                <div
                  className={classNames(styles.linkTitle, styles.blogTitle)}
                  /* eslint-disable-next-line react/no-danger */ dangerouslySetInnerHTML={{
                    __html: titleDescription?.description,
                  }}
                />
                {button?.link && (
                  <div className={styles.blogCTALink}>
                    <Link
                      href={`${button?.link}`}
                      onClick={onClick}
                      className={styles.ctaLink}
                    >
                      {button?.title}
                      <span>
                        <Image
                          src={ArrowRight}
                          alt="right Arrow"
                          width={24}
                          height={24}
                        />
                      </span>
                    </Link>
                  </div>
                )}
              </div>
            </div>
          </Col>
        </Row>
      </div>
      <div className={styles.bottom_border}></div>
    </>
  );
}
