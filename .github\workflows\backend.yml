# name: Deploy Lambda Function

# on:
#   push:
#     branches:
#       - development
#     paths:
#       - Backend/**

# jobs:
#   deploy:
#     runs-on: ubuntu-latest

#     steps:
#     - name: Checkout code
#       uses: actions/checkout@v4

#     - name: Install dependencies
#       run: |
#         cd Backend
#         npm install

#     - name: Set up AWS CLI
#       uses: aws-actions/configure-aws-credentials@v3
#       with:
#         aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
#         aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
#         aws-region: ap-south-1

#     - name: Zip only node_modules
#       run: |
#         cd Backend
#         zip -r ../dependencies.zip node_modules

#     - name: Upload dependencies ZIP to S3
#       run: |
#         aws s3 cp dependencies.zip s3://mtl-lambda-code-bucket/lambda/dependencies.zip


name: Deploy Lambda Function

on:
  push:
    branches:
      - development
    paths:
      - Backend/**

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Install dependencies
      run: |
        cd Backend
        npm install

    - name: Set up AWS CLI
      uses: aws-actions/configure-aws-credentials@v3
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: us-east-1

    - name: Zip Lambda function
      run: |
        cd Backend
        zip -r ../my_lambda_function.zip .

    - name: Upload ZIP to S3
      run: |
        aws s3 cp my_lambda_function.zip s3://mtl-lambda-code-bucket/lambda/my_lambda_function.zip
