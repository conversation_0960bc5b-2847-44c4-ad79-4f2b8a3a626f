@value variables: "@styles/variables.module.css";
@value gray300, colorBlack, colorWhite, brandColorOne, brandColorTwo, brandColorThree, brandColorFour, brandColorFive from variables;

@value breakpoints: "@styles/breakpoints.module.css";
@value breakpoint-xl, breakpoint-md from breakpoints;

.form {
  margin: 2.8125rem 2rem;
  max-width: 700px;

  @media screen and (max-width: breakpoint-xl) {
    margin: 2.8125rem auto 0 auto;
  }

  @media screen and (max-width: breakpoint-md) {
    margin: 2.8125rem 2rem 6rem 2rem;
  }
}

.inputFields {
  margin: 1.25rem 0;
}

.firstName {
  margin-right: 1.25rem;

  @media screen and (max-width: breakpoint-md) {
    margin-right: 0;
  }
}

.firstName,
.lastName {
  width: 100%;

  @media screen and (max-width: breakpoint-md) {
    margin-top: 1.25rem;
  }
}

.nameFields {
  display: flex;

  @media screen and (max-width: breakpoint-md) {
    display: block;
  }
}

.input,
.textarea {
  background-color: colorWhite;
  border: 1px solid transparent;
  border-radius: 3px;
  padding: 0.625rem;
  width: 100%;
  font-size: 14px;
  line-height: 24px;
}

.input {
  height: 41px;
}

.textarea {
  height: 134px;
  resize: none;
}

.input:focus,
.textarea:focus {
  outline: none;
}

.checkbox {
  display: flex;
  margin-bottom: 0;
  font-size: 14px;
  line-height: 24px;
}

.input[type='checkbox'] {
  height: 22px;
  width: 22px;
  border-radius: 2px;
  margin-top: 0;
  margin-right: 0.75rem;
}

.button {
  color: colorBlack !important;
  position: relative !important;
  margin-top: 1.875rem !important;
  padding: 13px 34.5px !important;
  border-radius: 3px !important;
  font-weight: 600 !important;
  font-size: 16px !important;
  line-height: 24px !important;
  border: 2px solid transparent !important;
  cursor: pointer !important;

  background-image: linear-gradient(gray300, gray300),
    linear-gradient(
      93.12deg,
      brandColorOne 0%,
      brandColorTwo 30.56%,
      brandColorThree 53.47%,
      brandColorFour 75.75%,
      brandColorFive 100%
    ) !important;
  background-origin: border-box !important;
  background-clip: padding-box, border-box !important;
  z-index: 1 !important;

  @media screen and (max-width: breakpoint-md) {
    width: 100% !important;
  }
}

.container_spinner {
  position: relative;
  margin-top: 1.875rem;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 3px;
  font-weight: 600;
  font-size: 16px;
  line-height: 24px;
  border: 2px solid transparent;
  cursor: pointer;
  width: 143px;
  height: 54px;

  background-image: linear-gradient(gray300, gray300),
    linear-gradient(
      93.12deg,
      brandColorOne 0%,
      brandColorTwo 30.56%,
      brandColorThree 53.47%,
      brandColorFour 75.75%,
      brandColorFive 100%
    );
  background-origin: border-box;
  background-clip: padding-box, border-box;
  z-index: 1;

  @media screen and (max-width: breakpoint-md) {
    width: 100%;
  }
}

.button:hover {
  box-shadow:
    3px 3px 13px 0px #ffac7d82,
    6px -2px 11px 0px #ff72ae8f,
    -6px 3px 11px 1px #ffbb0057;
}

.spinner {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background: linear-gradient(
    93deg,
    brandColorOne 0%,
    brandColorTwo 30.56%,
    brandColorThree 53.47%,
    brandColorFour 75.75%,
    brandColorFive 100%
  );

  -webkit-mask-image: radial-gradient(
    circle,
    rgba(0, 0, 0, 0) 55%,
    rgba(0, 0, 0, 1) 60%
  );
  animation: spin 0.8s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

@media screen and (max-width: breakpoint-md) {
  .firstName,
  .lastName {
    margin-top: 1.25rem;
  }
}

.errorInput {
  border: 1px solid #ff0000;
}

.errorMessages {
  position: absolute;
  margin-top: 0.625rem;
  font-weight: 500;
  font-size: 16px;
  line-height: 25.6px;
  color: #ff0000;
}

.label {
  margin-bottom: 0.625rem;
}

.consentLabel {
  user-select: none;
  cursor: pointer;
}

.errorLabel {
  color: #ff0000;
}

.phoneInput {
  border: 1px solid transparent !important;
  width: 100% !important;
  height: 41px !important;
  box-shadow: none !important;
}

.errorPhoneInput {
  border: 1px solid #ff0000 !important;
}

.phoneButton {
  border-width: 0px !important;
  background-color: colorWhite !important;
}

.errorPhoneButton {
  border-width: 1px 0px 1px 1px !important;
  border-color: #ff0000 !important;
}

.phoneButton > div,
.phoneButton > div:hover {
  background-color: white !important;
}

.phoneButton > div > div > div {
  border-bottom: none !important;
  border-top: 5px solid black !important;
}
