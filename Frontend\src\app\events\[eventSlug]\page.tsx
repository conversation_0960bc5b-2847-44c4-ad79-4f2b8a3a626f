import React from 'react';
import CaseStudyHeroSection from '@components/CaseStudyHeroSection';
import ContactUsForm from '@components/ContactUsForm';
import MeetOurTeam from '@components/MeetOurTeam';
import Offering from '@components/OfferingCard';
import RichText from '@components/RichText';
import WhyChooseMTL from '@components/WhyChooseMTL';
import RichResults from '@components/RichResults';
import seoSchema from '@utils/seoSchema';
import { notFound } from 'next/navigation';
import fetchFromStrapi from '@utils/fetchFromStrapi';

export async function generateStaticParams() {
  const eventsPageResponse = await fetchFromStrapi('event-main-pages');
  const eventSlug = eventsPageResponse.data.map(res => ({
    eventSlug: res.attributes.slug,
  }));
  return eventSlug;
}

export async function fetchEventsPageData(slug: string) {
  const queryString = `filters[slug][$eq]=${slug}&populate=hero_section.hero_image,hero_section.button,about_event,offerings.card,why_choose_mtl.whyChooseMtlCards,form.formFields,form.button,meet_our_people.our_people.logo,meet_our_people.our_people.image,seo.schema`;
  return await fetchFromStrapi('event-main-pages', queryString);
}

async function getFormData() {
  const queryString = 'populate=form.formFields&populate=form.button';
  return await fetchFromStrapi('form', queryString);
}

export async function generateMetadata({
  params,
}: {
  params: { eventSlug: string };
}) {
  const { eventSlug: slug } = params;
  const queryString = `filters[slug][$eq]=${slug}&populate=seo&populate=seo.image&populate=seo.metaProperties&populate=seo.keywords&populate=seo.schema`;
  const seoFetchedData = await fetchFromStrapi('event-main-pages', queryString);
  const seoData = seoFetchedData?.data[0]?.attributes?.seo;

  // Return the metadata
  return seoSchema(seoData);
}

export default async function Services({ params }: { params: any }) {
  const { eventSlug } = params;
  const eventsData = await fetchEventsPageData(eventSlug);
  const formData = await getFormData();

  // Check if events page data exists, otherwise return 404
  if (!eventsData?.data || eventsData?.data.length === 0) {
    notFound();
  }

  return (
    <>
      {eventsData?.data[0]?.attributes?.seo && (
        <RichResults data={eventsData?.data[0]?.attributes?.seo} />
      )}
      {eventsData?.data[0]?.attributes?.hero_section && (
        <CaseStudyHeroSection
          heroData={eventsData?.data[0]?.attributes?.hero_section}
          variant="events_page"
          eventPageSlug={eventsData?.data[0]?.attributes?.slug}
          eventPageName={eventsData?.data[0]?.attributes?.page_name}
        />
      )}
      {eventsData?.data[0]?.attributes?.about_event && (
        <RichText
          richTextData={eventsData?.data[0]?.attributes?.about_event}
          variant="caseStudy"
          position="center"
        />
      )}
      {eventsData?.data[0]?.attributes?.meet_our_people && (
        <MeetOurTeam
          meetOurTeamData={eventsData?.data[0]?.attributes?.meet_our_people}
          variant="about-us"
        />
      )}
      {eventsData?.data[0]?.attributes?.offerings && (
        <Offering data={eventsData?.data[0]?.attributes?.offerings} />
      )}
      {eventsData?.data[0]?.attributes?.why_choose_mtl && (
        <WhyChooseMTL data={eventsData?.data[0]?.attributes?.why_choose_mtl} />
      )}
      {formData?.data?.attributes?.form && (
        <ContactUsForm
          formData={formData?.data?.attributes?.form}
          source="Events"
        />
      )}
    </>
  );
}
