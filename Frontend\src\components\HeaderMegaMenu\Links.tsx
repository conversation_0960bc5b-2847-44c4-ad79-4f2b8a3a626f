import React from 'react';
import { Col, Row } from 'react-bootstrap';
import styles from '@components/Header/Header.module.css';
import classNames from '@utils/classNames';

import MenuLink from './MenuLink';

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export default function Links({ links, onClick, variant = 'default' }: any) {
  const forCareers = variant === 'careers' ? true : '';
  return (
    <>
      <div className={styles.megaMenuContent}>
        <div className={styles.menuWrapper}>
          <Row>
            {links?.map(link => (
              <Col
                className={classNames(
                  'col-sm-12 col-md-6 col-lg-3 col-xl-3',
                  styles.flexDirectionColumn,
                )}
                key={link?.id}
              >
                <MenuLink
                  linkTitle={link?.title}
                  href={link?.link}
                  onClick={onClick}
                  forCareers={forCareers}
                />
              </Col>
            ))}
          </Row>
        </div>
      </div>
      <div className={styles.bottom_border}></div>
    </>
  );
}
