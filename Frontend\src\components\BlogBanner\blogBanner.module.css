@value variables: "@styles/variables.module.css";
@value gray700, oneSpace, fontWeight700, bodyTextXXSmall, halfSpace, twoSpace, threeSpace, fourSpace, fiveSpace, colorBlack, colorWhite, fifteenSpace, grayBorder, brandColorOne, brandColorTwo, brandColorThree, brandColorFour, brandColorFive, bodyTextXXXSSmall from variables;
@value breakpoints: "@styles/breakpoints.module.css";
@value breakpoint-sm, breakpoint-md, breakpoint-xl-1406, breakpoint-md-767, breakpoint-lg, breakpoint-xl-2000, breakpoint-xl-1800, breakpoint-sm-450, breakpoint-sm-550, breakpoint-sm-320, breakpoint-xl-1024, breakpoint-xl-1440, breakpoint-lg-991px from breakpoints;

.bannerPrimaryWithButton {
  position: relative;
  overflow: hidden;
  padding: 0px 260px;
  display: flex;
  gap: 30px;
  align-items: center;
  justify-content: space-between;
  color: colorWhite;
  height: 354px;

  @media screen and (max-width: breakpoint-xl-1440) {
    padding: 0px 150px;
  }

  @media screen and (max-width: breakpoint-md) {
    padding: 0px 80px;
  }

  @media screen and (max-width: breakpoint-sm) {
    padding: 40px;
    flex-direction: column;
    align-items: normal;
    height: auto;
  }
}

.background_image {
  object-fit: cover;
  z-index: -1;
}

.categoryAndTitle {
  display: flex;
  flex-direction: column;
  gap: 25px;

  @media screen and (max-width: breakpoint-sm) {
    gap: 20px;
  }
}

.category {
  width: fit-content;
  padding: 5px;
  border-radius: 3px;
  background: colorBlack;
  color: colorWhite;
  font-size: 14px;
  font-style: normal;
  font-weight: fontWeight700;
  line-height: 123%;
}

.title {
  color: colorWhite;
  font-size: 36px;
  font-style: normal;
  font-weight: fontWeight700;
  line-height: 115%;
}