'use client';

import React, { useState, useRef, useEffect } from 'react';
import { Container } from 'react-bootstrap';
import styles from './TechStack.module.css';
import Heading from '@components/Heading';
import Image from 'next/image';
import useMediaQueryState from '@hooks/useMediaQueryState';
import breakpoints from '@styles/breakpoints.module.css';

export default function TechStack({ data }) {
  const [activeTab, setActiveTab] = useState(0);
  const scrollContainerRef = useRef(null);
  const [isScrollable, setIsScrollable] = useState(false);
  const isDragging = useRef(false);
  const startX = useRef(0);
  const scrollLeft = useRef(0);
  const isMobile = useMediaQueryState({
    query: `(max-width: ${breakpoints['breakpoint-sm-450']})`,
  });
  const activeTabContent = data?.tab[activeTab];

  useEffect(() => {
    const updateScrollability = () => {
      const scrollContainer = scrollContainerRef.current;
      if (scrollContainer) {
        setIsScrollable(
          scrollContainer.scrollWidth > scrollContainer.clientWidth,
        );
      }
    };

    updateScrollability(); // Check on mount

    window.addEventListener('resize', updateScrollability);
    return () => {
      window.removeEventListener('resize', updateScrollability);
    };
  }, [data?.tab]);

  useEffect(() => {
    if (!isMobile) return;
    const container = scrollContainerRef.current;

    const handleScroll = () => {
      const items = container.querySelectorAll(`.${styles.tech_tabs_wrapper}`);
      let leftmostIndex = -1;
      let minLeft = Infinity;

      items.forEach((item, index) => {
        const itemRect = item.getBoundingClientRect();
        const containerRect = container.getBoundingClientRect();

        const isInView =
          itemRect.right > containerRect.left &&
          itemRect.left < containerRect.right;

        if (isInView && itemRect.left < minLeft) {
          minLeft = itemRect.left;
          leftmostIndex = index;
        }
      });

      if (leftmostIndex !== -1 && leftmostIndex !== activeTab) {
        setActiveTab(leftmostIndex);
      }
    };

    container.addEventListener('scroll', handleScroll, { passive: true });

    return () => container.removeEventListener('scroll', handleScroll);
  }, [activeTab, isMobile]);

  const scrollToTab = direction => {
    const container = scrollContainerRef.current;
    const items = container.querySelectorAll(`.${styles.tech_tabs}`);
    const targetIndex = direction === 'next' ? activeTab + 1 : activeTab - 1;

    if (items[targetIndex]) {
      !isMobile && setActiveTab(targetIndex);
      items[targetIndex].scrollIntoView({
        behavior: 'smooth',
        inline: 'start',
        block: 'nearest',
      });
    }
  };

  // Drag Scrolling Handlers
  const handleMouseDown = e => {
    isDragging.current = true;
    startX.current = e.pageX - scrollContainerRef.current.offsetLeft;
    scrollLeft.current = scrollContainerRef.current.scrollLeft;
    scrollContainerRef.current.style.cursor = 'grabbing';
  };

  const handleMouseMove = e => {
    if (!isDragging.current) return;
    e.preventDefault();
    const x = e.pageX - scrollContainerRef.current.offsetLeft;
    const walk = (x - startX.current) * 2; // Adjust speed
    scrollContainerRef.current.scrollLeft = scrollLeft.current - walk;
  };

  const handleMouseUp = () => {
    isDragging.current = false;
    scrollContainerRef.current.style.cursor = 'grab';
  };

  const handleMouseLeave = () => {
    isDragging.current = false;
    scrollContainerRef.current.style.cursor = 'grab';
  };

  return (
    <Container fluid className={styles.main_container}>
      <div className={styles.inner_container}>
        <Heading
          headingType="h2"
          title={data?.title}
          position="center"
          className={styles.ourServiceTitle}
        />
      </div>
      <div className={styles.scrollWrapper}>
        {isScrollable && (
          <button
            className={styles.scrollButton}
            onClick={() => scrollToTab('prev')}
          >
            <Image
              src="https://cdn.marutitech.com//arrow_left_571ede1db4.svg"
              alt="Scroll Left"
              width={40}
              height={40}
            />
          </button>
        )}
        <section
          ref={scrollContainerRef}
          className={styles.scrollContainer}
          onMouseDown={handleMouseDown}
          onMouseMove={handleMouseMove}
          onMouseUp={handleMouseUp}
          onMouseLeave={handleMouseLeave}
        >
          <div className={styles.scrollContent}>
            {data?.tab?.map((tab, index) => (
              <div className={styles.tech_tabs_wrapper} key={tab?.id}>
                <div
                  className={`${styles.tech_tabs} ${activeTab === index ? styles.activeTab : ''}`}
                  onClick={() => setActiveTab(index)}
                  id={index}
                  role="button"
                  tabIndex={0}
                >
                  {tab?.tab_title}
                </div>
              </div>
            ))}
          </div>
        </section>
        {isScrollable && (
          <button
            className={styles.scrollButton}
            onClick={() => scrollToTab('next')}
          >
            <Image
              src="https://cdn.marutitech.com//arrow_right_39f0ac1581.svg"
              alt="Scroll Right"
              width={40}
              height={40}
            />
          </button>
        )}
      </div>

      <div className={styles.tabContent} key={activeTab}>
        {activeTabContent?.logo_url?.map(logoData => (
          <div className={styles.box} key={logoData?.title}>
            <div className={styles.iconContainer}>
              <img
                src={logoData?.url}
                alt={logoData?.title}
                className={styles.iconImage}
              />
            </div>
            <div className={styles.iconTitle}>{logoData?.title}</div>
          </div>
        ))}
      </div>
    </Container>
  );
}
