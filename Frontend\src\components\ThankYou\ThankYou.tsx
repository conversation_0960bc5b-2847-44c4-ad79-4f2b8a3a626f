'use client';

import React from 'react';
import { Container } from 'react-bootstrap';
import Button from '@components/Button';
import Heading from '@components/Heading';
import { useRouter } from 'next/navigation';

import styles from './ThankYou.module.css';

export default function ThankYou({ dataThankYou }) {
  const router = useRouter();

  return (
    <>
      <Container fluid className={styles.container}>
        <Heading
          headingType="h1"
          title={dataThankYou?.title}
          className={styles.title}
        />
        <div
          className={styles.description}
          dangerouslySetInnerHTML={{ __html: dataThankYou?.description }}
        ></div>
        <Button
          className={styles.button}
          label={dataThankYou?.button?.title}
          type="button"
          onClick={() => router.push(`${dataThankYou?.button?.link}`)}
        />
      </Container>
    </>
  );
}
