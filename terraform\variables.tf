# variable "application_name" {
#   description = "Application name"
#   type        = string
#   default     = "mtl-static-site"
# }
# variable "aws_access_key" {
#   type        = string
#   sensitive   = true
#   description = "AWS access key"
# }
# variable "aws_secret_key" {
#   type        = string
#   sensitive   = true
#   description = "AWS secret key"
# }
variable "aws_region" {
  description = "AWS region"
  type        = string
  default     = "ap-south-1"
}
variable "common_tags" {
  description = "Common tags for all resources"
  type        = map(string)
}
# variable "bucket_name" {
#   description = "S3 Bucket name"
#   type        = string
# }
variable "environment" {
  description = "dev"
  type        = string
}

# variable "domain_name" {
#   default = "cdn.marutitech.com"
# }

# variable "maruti_site_cdn_bucket_name" {
#   description = "Maruti Site CDN Bucket Name"
#   type        = string
# }

variable "image_extensions" {
  default = ["svg", "jpg", "png", "webp"]
  type    = list(string)
}
variable "mtl-cf-custom-auth" {
  description = "Secret token used by CloudFront to access API Gateway"
  type        = string
}

# variable "ssm_parameters" {
#   type        = map(string)
#   description = "Key-value map to be stored as a single JSON parameter"
# }
