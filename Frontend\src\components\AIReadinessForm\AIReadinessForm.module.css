@value variables: "@styles/variables.module.css";
@value colorBlack, colorWhite, gray, brandColorOne, brandColorTwo, brandColorThree, brandColorFour, brandColorFive, gray300 from variables;
@value breakpoints: "@styles/breakpoints.module.css";
@value breakpoint-sm-450, breakpoint-md, breakpoint-xl-1024, breakpoint-xl-1400 from breakpoints;

.heading > h2 {
  font-weight: 600;
  font-size: 32px;
  line-height: 132%;
  padding: 20px 0;
  border-bottom: 2px solid black;
}

.formWrapper {
  padding: 40px 10px 0;
  display: flex;
  flex-direction: column;
  gap: 2.5rem;

  @media (max-width: 1170px) {
    padding: 2.5rem 2rem;
  }

  @media (max-width: breakpoint-sm-450) {
    padding: 1rem;
  }
}

.form {
  display: flex;
  flex-direction: column;
  gap: 2.5rem;

  @media (min-width: breakpoint-xl-1400) {
    width: 1192px;
  }
}

.formFields {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.personalDetailsWrapper {
  display: flex;
  gap: 24px;
  flex-direction: column;
}

.row {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  row-gap: 40px;
}

.nameAndInputWrapper {
  width: 48%;
  display: flex;
  justify-content: space-between;

  @media screen and (max-width: 1170px) {
    width: 100%;
    padding: 0 5rem;
  }

  @media screen and (max-width: breakpoint-xl-1024) {
    width: 100%;
    padding: 0;
  }
}

.firstRow {
  flex-direction: row;

  @media (max-width: 1170px) {
    flex-direction: column !important;
    gap: 24px;
  }
}

.formLabel {
  width: 220px;
  color: colorBlack;
  font-size: 26px;
  font-style: normal;
  font-weight: 500;
  line-height: 164%;

  @media screen and (max-width: breakpoint-md) {
    width: 170px;
    font-weight: 400;
    font-size: 18px;
    line-height: 168%;
  }
}

.formInput {
  width: 300px;
  background: gray300;
  color: colorBlack;
  border-radius: 3px;
  height: 41px;
  border: none;
  padding: 10px;

  @media screen and (max-width: 1170px) {
    width: 68%;
  }

  @media screen and (max-width: breakpoint-sm-450) {
    width: 48%;
  }
}

.phoneInputWrapper {
  width: 300px;

  @media screen and (max-width: 1170px) {
    width: 68%;
  }

  @media screen and (max-width: breakpoint-sm-450) {
    width: 48%;
  }
}

.formInputPhone {
  background: gray300 !important;
  color: colorBlack !important;
  height: 41px !important;
  width: 100% !important;
  border: none !important;
  padding: 10px;
  box-shadow: none !important;
}

.formInputPhone_dial_icon {
  background-color: gray300 !important;
  color: colorBlack !important;
  height: 41px !important;
  padding: 10px;
  border: none !important;
  box-shadow: none !important;
}

.formInputPhone_dial_icon > div,
.formInputPhone_dial_icon > div:hover {
  background-color: gray300 !important;
  color: colorBlack !important;
}

.formInputPhone_dial_icon > div > div > div {
  border-bottom: none !important;
  border-top: 5px solid colorBlack !important;
}

.ph_number_countries_dropdown {
  color: colorBlack !important;
}

.formInputForHowCanWeHelpYou {
  height: 82px;
  max-height: 250px;
}

.formInput:focus-visible {
  outline: none;
  margin: 0;
}

.consentRow {
  display: flex;
  gap: 12px;
}

.consentText {
  display: flex;
  gap: 10px;
  user-select: none;
  cursor: pointer;
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}

.submitButtonRow {
  align-items: center;
  justify-content: start;
  gap: 24px;

  @media (max-width: breakpoint-md) {
    flex-direction: column;
    gap: 24px;
  }
}

.submitButton {
  padding: 16px 36px !important;

  @media (max-width: breakpoint-md) {
    width: 100%;
  }
}

.submitButton > div {
  font-size: 20px;
  font-style: normal;
  font-weight: 600;
  line-height: 160%;
}

.submitButton::before {
  border-radius: 6px;
  padding: 2px;
}

.linkedInButton {
  display: flex;
  gap: 8px;
  align-items: center;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  text-decoration: none;
}

.errorInput {
  border: 1px solid #ff0000 !important;
}

.errorMessages {
  font-weight: 500;
  font-size: 16px;
  line-height: 25.6px;
  color: #ff0000;
}

.errorLabel {
  color: #ff0000;
  font-size: 26px;

  @media screen and (max-width: breakpoint-md) {
    width: 170px;
    font-weight: 400;
    font-size: 18px;
    line-height: 168%;
  }
}

.errorLabel_consentText {
  color: #ff0000;
  font-size: 16px;
}

.container_spinner {
  position: relative;
  width: 170px;
  height: 68px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 3px;
  border: 2px solid transparent;
  cursor: pointer;
  background-image: linear-gradient(colorWhite, colorWhite),
    linear-gradient(
      93.12deg,
      brandColorOne 0%,
      brandColorTwo 30.56%,
      brandColorThree 53.47%,
      brandColorFour 75.75%,
      brandColorFive 100%
    );
  background-origin: border-box;
  background-clip: padding-box, border-box;
  z-index: 1;
}

.spinner {
  width: 35px;
  height: 35px;
  border-radius: 50%;
  background: linear-gradient(
    93deg,
    #febe10 0%,
    #f47a37 30.56%,
    #f05443 53.47%,
    #d91a5f 75.75%,
    #b41f5e 100%
  );

  -webkit-mask-image: radial-gradient(
    circle,
    rgba(0, 0, 0, 0) 55%,
    rgba(0, 0, 0, 1) 60%
  );
  animation: spin 0.8s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.result_button {
  padding: 13px 35.5px;
  border: none !important;

  font-weight: 600;
  font-size: 20px;
  line-height: 148%;

  color: colorWhite;
  background: linear-gradient(
    90deg,
    brandColorOne 0%,
    brandColorTwo 30.56%,
    brandColorThree 53.47%,
    brandColorFour 75.75%,
    brandColorFive 100%
  );
}

.consentText > input {
  border-radius: 2px;
}
