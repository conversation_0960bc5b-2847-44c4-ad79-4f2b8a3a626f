'use client';

import React from 'react';
import { Container } from 'react-bootstrap';
import Link from 'next/link';
import Button from '@components/Button';
import Heading from '@components/Heading';
import { useRouter } from 'next/navigation';

import styles from './Cta.module.css';
import { CTATypes } from './types';

export default function CTA({ data, variant }: CTATypes) {
  const router = useRouter();

  const handleButtonClick = () => {
    if (variant === 'scrollToContactForm') {
      const contactFormElement = document.getElementById('contact-us-form');
      if (contactFormElement) {
        contactFormElement.scrollIntoView({ behavior: 'smooth' });
      }
    } else {
      router.push(`${data?.ctaLink}`);
    }
  };
  return (
    <section className={styles.ctaContainer}>
      {variant !== 'downloadOurBrand' ? (
        <Container className={styles.ctaWrapper}>
          <Heading
            richTextValue={data?.ctaTitle}
            headingType="h2"
            className={styles.ctaHeading}
          />
          <div className={styles.ctaBtn}>
            <Button
              label={data?.ctaButtonText}
              className={styles.btn}
              onClick={handleButtonClick}
            />
          </div>
        </Container>
      ) : (
        <Container className={styles.ctaWrapper}>
          <Heading
            richTextValue={data?.ctaTitle}
            headingType="h3"
            className={styles.ctaHeading}
          />
          <Link
            href={data?.ctaLink}
            target="_blank"
            typeof="application/pdf"
            prefetch={false}
            download
            className={styles.downloadLinkWrapper}
          >
            <div className={styles.downloadLink}>{data?.ctaButtonText}</div>
          </Link>
        </Container>
      )}
    </section>
  );
}
