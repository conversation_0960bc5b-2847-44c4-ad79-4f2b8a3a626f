import React from 'react';
import classNames from '@utils/classNames';

import styles from './SliderButtons.module.css';
// type PropType = HTMLButtonElement;

// eslint-disable-next-line @typescript-eslint/no-explicit-any
function NextButton(props: any) {
  const { ...restProps } = props;

  return (
    <button
    aria-label='carousel-next-button'
      className={classNames(
        styles.embla__button,
        styles['embla__button--Next'],
      )}
      type="button"
      // eslint-disable-next-line react/jsx-props-no-spreading
      {...restProps}
    >
      <svg
        className={styles.embla__button__svg}
        viewBox="0 0 40 40"
        fill="none"
        height={40}
        width={40}
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M8.33341 20L31.6667 20"
          stroke="black"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M20.0001 8.33335L31.6667 20L20.0001 31.6667"
          stroke="black"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </svg>
    </button>
  );
}
export default NextButton;
