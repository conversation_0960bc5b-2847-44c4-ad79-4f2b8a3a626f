name: <PERSON><PERSON><PERSON> and Deploy CloudFront Token

on:
  workflow_dispatch: {}
  schedule:
  # At 00:00 UTC on the 1st of Jan, Apr, Jul, Oct
    # - cron: '0 0 1 1,4,7,10 *'
    - cron: '*/1 * * * *'  # Every 1 minutes

jobs:
  rotate-and-deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout Code
        uses: actions/checkout@v3
        with:
          ref: development

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ap-south-1

      - name: Generate New Token
        id: token
        run: |
          TOKEN=$(openssl rand -hex 32)
          FULL_TOKEN="mtl-cf-auth-$TOKEN"
          echo "token=$FULL_TOKEN" >> $GITHUB_OUTPUT

      - name: Store Token in SSM
        run: |
          aws ssm put-parameter \
            --name "/auth/mtl-cf-custom-auth" \
            --value "${{ steps.token.outputs.token }}" \
            --type "SecureString" \
            --overwrite

      - name: Terraform Init
        run: terraform init

      - name: Terraform Plan
        env:
          TF_VAR_cf_auth_token: ${{ steps.token.outputs.token }}
        run: terraform plan

      - name: Terraform Apply
        env:
          TF_VAR_cf_auth_token: ${{ steps.token.outputs.token }}
        run: terraform apply -auto-approve
