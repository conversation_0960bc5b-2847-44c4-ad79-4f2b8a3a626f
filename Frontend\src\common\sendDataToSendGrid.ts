const axios = require('axios');

const sendDataToSendGrid = async (
  to,
  from,
  replyTo,
  templateId,
  dynamicData,
) => {
  let config = {
    headers: {
      'Content-Type': 'application/json',
      Authorization: 'Bearer ' + process.env.NEXT_PUBLIC_SENDGRID_API_KEY,
    },
  };

  let mailTo = process.env.NEXT_PUBLIC_SECOND_RECIPENT
    ? [
        {
          email: to,
        },
        {
          email: process.env.NEXT_PUBLIC_SECOND_RECIPENT,
        },
      ]
    : [
        {
          email: to,
        },
      ];

  const data = {
    from: {
      email: from,
    },
    reply_to: {
      email: replyTo,
    },
    personalizations: [
      {
        to: mailTo,
        dynamic_template_data: {
          lead: dynamicData,
        },
      },
    ],
    template_id: templateId,
  };

  try {
    let result = await axios.post(
      'https://api.sendgrid.com/v3/mail/send',
      data,
      config,
    );
    if (result.status === 200 || result.status === 202) {
      return {
        status: true,
        message: 'Sendgrid API message: Email sent successfully.',
      };
    } else {
      throw 'Error';
    }
  } catch (error) {
    console.error('--------error', error);
    return {
      status: false,
      error: `Sendgrid API message: Error while sending email.`,
    };
  }
};

export default sendDataToSendGrid;
