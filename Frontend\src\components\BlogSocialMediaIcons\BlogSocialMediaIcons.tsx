import React from 'react';
import style from './blogSocialMediaIcons.module.css';

const SocialMediaIcons = props => {
  const fbshare = ` https://www.facebook.com/sharer/sharer.php?u= ${props.blogurl}`;
  const twittershare = `https://twitter.com/intent/tweet?text= ${props.blogurl}`;

  const linkedinshare = `https://www.linkedin.com/shareArticle?mini=true&url=${props.blogurl}`;

  // const pinterestshare = `https://pinterest.com/pin/create/button/?url=${props.blogurl}`;

  const openFBPopUp = () => {
    if (typeof window !== 'undefined') {
      window.open(
        fbshare,
        '_blank',
        'width=600,height=500 left=500px top=200px',
      );
    }
  };
  const shareOnTwitter = () => {
    if (typeof window !== 'undefined') {
      window.open(
        twittershare,
        '_blank',
        'width=600,height=500 left=500px top=200px',
      );
    }
  };
  const shareOnLinkedIn = () => {
    if (typeof window !== 'undefined') {
      window.open(
        linkedinshare,
        '_blank',
        'width=600,height=500 left=500px top=200px',
      );
    }
  };
  const shareOnPinterest = () => {
    // if (typeof window !== "undefined") {
    //   window.open(pinterestshare, "_blank", "width=600,height=600");
    // }
    var e = document.createElement('script');

    e.setAttribute('type', 'text/javascript');
    e.setAttribute('charset', 'UTF-8');
    e.setAttribute(
      'src',
      'https://assets.pinterest.com/js/pinmarklet.js?r=' + Math.random() * 9999,
    );
    document.body.appendChild(e);
  };
  return (
    <>
      {props.variant === 'horizontal' && (
        <div className={style.social_media_icons}>
          {props.linkedin && (
            <a href={props?.linkedin_link} target="_blank" rel="noopener">
              <div>
                <svg
                  width="37"
                  height="37"
                  viewBox="0 0 37 37"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                  role="img"
                  aria-label="linkedinshare"
                  focusable="false"
                >
                  <path
                    d="M18.5 36C8.8347 36 1 28.1653 1 18.5C1 8.8347 8.8347 1 18.5 1C28.1653 1 36 8.8347 36 18.5C36 28.1653 28.1653 36 18.5 36Z"
                    stroke="#141613"
                    strokeWidth="0.43"
                    strokeMiterlimit="10"
                  />
                  <path
                    d="M14.2361 15.0273H11.0269V25.4008H14.2361V15.0273Z"
                    fill="#141613"
                  />
                  <path
                    d="M12.6165 13.6712C13.6636 13.6712 14.5149 12.8124 14.5149 11.7577C14.5149 10.703 13.6636 9.84424 12.6165 9.84424C11.5693 9.84424 10.718 10.703 10.718 11.7577C10.718 12.8124 11.5693 13.6712 12.6165 13.6712Z"
                    fill="#141613"
                  />
                  <path
                    d="M19.3739 19.9615C19.3739 18.5 20.0443 17.6337 21.3325 17.6337C22.5153 17.6337 23.0803 18.4699 23.0803 19.9615V25.4081H26.2744V18.839C26.2744 16.0592 24.7 14.7183 22.5002 14.7183C20.3005 14.7183 19.3739 16.4359 19.3739 16.4359V15.0347H16.2927V25.4081H19.3739V19.9615Z"
                    fill="#141613"
                  />
                </svg>
              </div>
            </a>
          )}
          {props.instagram && (
            <div>
              <svg
                width="37"
                height="37"
                viewBox="0 0 37 37"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M18.5 36C8.8347 36 1 28.1653 1 18.5C1 8.8347 8.8347 1 18.5 1C28.1653 1 36 8.8347 36 18.5C36 28.1653 28.1653 36 18.5 36Z"
                  stroke="#141613"
                  strokeWidth="0.43"
                  strokeMiterlimit="10"
                />
                <path
                  d="M18.5 11.5241C20.7751 11.5241 21.0387 11.5317 21.9352 11.5769C22.7639 11.6145 23.2159 11.7501 23.5172 11.8707C23.9165 12.0213 24.1952 12.2097 24.4966 12.5035C24.7979 12.8048 24.9787 13.0835 25.1294 13.4828C25.2424 13.7842 25.3855 14.2362 25.4232 15.0648C25.4608 15.9613 25.4759 16.2325 25.4759 18.5C25.4759 20.7676 25.4684 21.0388 25.4232 21.9353C25.3855 22.7639 25.2499 23.2159 25.1294 23.5173C24.9787 23.9165 24.7904 24.1953 24.4966 24.4966C24.2028 24.7979 23.9165 24.9787 23.5172 25.1294C23.2159 25.2424 22.7639 25.3855 21.9352 25.4232C21.0387 25.4609 20.7675 25.4759 18.5 25.4759C16.2325 25.4759 15.9613 25.4684 15.0648 25.4232C14.2361 25.3855 13.7841 25.2499 13.4828 25.1294C13.0835 24.9787 12.8048 24.7904 12.5034 24.4966C12.2021 24.1953 12.0213 23.9165 11.8706 23.5173C11.7576 23.2159 11.6145 22.7639 11.5768 21.9353C11.5392 21.0388 11.5241 20.7676 11.5241 18.5C11.5241 16.2325 11.5316 15.9613 11.5768 15.0648C11.6145 14.2362 11.7501 13.7842 11.8706 13.4828C12.0213 13.0835 12.2096 12.8048 12.5034 12.5035C12.8048 12.2021 13.0835 12.0213 13.4828 11.8707C13.7841 11.7577 14.2361 11.6145 15.0648 11.5769C15.9613 11.5392 16.2325 11.5241 18.5 11.5241ZM18.5 9.99487C16.1873 9.99487 15.901 10.0024 14.9895 10.0476C14.0855 10.0853 13.4677 10.2359 12.9253 10.4469C12.3678 10.6653 11.8932 10.9516 11.4186 11.4262C10.944 11.9008 10.6578 12.3754 10.4393 12.9329C10.2284 13.4753 10.0852 14.093 10.0476 14.997C10.0024 15.901 9.99483 16.1948 9.99483 18.5076C9.99483 20.8203 10.0024 21.1066 10.0476 22.0181C10.0852 22.9221 10.2359 23.5399 10.4393 24.0823C10.6578 24.6397 10.944 25.1143 11.4186 25.5889C11.8932 26.0635 12.3678 26.3498 12.9253 26.5683C13.4677 26.7792 14.0855 26.9223 14.9895 26.9675C15.8935 27.0052 16.1873 27.0203 18.5 27.0203C20.8127 27.0203 21.099 27.0127 22.0105 26.9675C22.9146 26.9299 23.5323 26.7792 24.0747 26.5683C24.6322 26.3498 25.1068 26.0635 25.5814 25.5889C26.056 25.1143 26.3422 24.6397 26.5607 24.0823C26.7716 23.5399 26.9148 22.9221 26.9524 22.0181C26.9901 21.1141 27.0052 20.8203 27.0052 18.5076C27.0052 16.1948 26.9976 15.9086 26.9524 14.997C26.9148 14.093 26.7641 13.4753 26.5607 12.9329C26.3422 12.3754 26.056 11.9008 25.5814 11.4262C25.1068 10.9516 24.6322 10.6653 24.0747 10.4469C23.5323 10.2359 22.9146 10.0928 22.0105 10.0476C21.1065 10.0099 20.8127 9.99487 18.5 9.99487Z"
                  fill="#141613"
                />
                <path
                  d="M18.5 14.1306C16.0893 14.1306 14.1307 16.0893 14.1307 18.5C14.1307 20.9106 16.0893 22.8693 18.5 22.8693C20.9107 22.8693 22.8694 20.9106 22.8694 18.5C22.8694 16.0893 20.9107 14.1306 18.5 14.1306ZM18.5 21.3325C16.9331 21.3325 15.6675 20.0594 15.6675 18.5C15.6675 16.9406 16.9406 15.6674 18.5 15.6674C20.0594 15.6674 21.3326 16.9406 21.3326 18.5C21.3326 20.0594 20.0594 21.3325 18.5 21.3325Z"
                  fill="#141613"
                />
                <path
                  d="M24.0597 13.9574C24.0597 14.5224 23.6001 14.9819 23.0351 14.9819C22.4701 14.9819 22.0106 14.5224 22.0106 13.9574C22.0106 13.3924 22.4701 12.9329 23.0351 12.9329C23.6001 12.9329 24.0597 13.3924 24.0597 13.9574Z"
                  fill="#141613"
                />
              </svg>
            </div>
          )}
          {props.twitter && (
            <a
              href={props?.twitter_link}
              target="_blank"
              rel="noopener"
              style={{ paddingLeft: '8px' }}
            >
              <div>
                <svg
                  width="37"
                  height="37"
                  viewBox="0 0 37 37"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                  role="img"
                  aria-label="twittershare"
                  focusable="false"
                >
                  <path
                    d="M18.5 36C8.8347 36 1 28.1653 1 18.5V18.5C1 8.83469 8.8347 1 18.5 1C28.1653 1 36 8.83469 36 18.5C36 28.1653 28.1653 36 18.5 36Z"
                    stroke="#141613"
                    strokeWidth="0.43"
                    strokeMiterlimit="10"
                  />
                  <path
                    d="M10.2284 24.4061C11.7275 25.3703 13.5129 25.9278 15.4264 25.9278C21.7243 25.9278 25.28 20.6092 25.0691 15.8331C25.732 15.3585 26.3046 14.7558 26.7641 14.0778C26.1539 14.349 25.4985 14.5298 24.813 14.6127C25.5136 14.1908 26.0484 13.5279 26.3046 12.7369C25.6492 13.1286 24.926 13.4074 24.15 13.558C23.5323 12.8951 22.6509 12.4883 21.6715 12.4883C19.4793 12.4883 17.8747 14.5298 18.3644 16.6542C15.5469 16.5111 13.0458 15.1626 11.3734 13.106C10.4845 14.6278 10.9139 16.6241 12.4206 17.6336C11.8631 17.6185 11.3433 17.4603 10.8838 17.2117C10.8461 18.7862 11.9761 20.2552 13.6033 20.5791C13.1287 20.7072 12.6014 20.7373 12.074 20.6394C12.5034 21.9879 13.7615 22.9672 15.2456 22.9973C13.8218 24.1123 12.0213 24.617 10.2208 24.3985L10.2284 24.4061Z"
                    fill="#141613"
                  />
                </svg>
              </div>
            </a>
          )}
          {props.facebook && (
            <div>
              <svg
                width="37"
                height="37"
                viewBox="0 0 37 37"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
                role="img"
                aria-label="facebookshare"
                focusable="false"
              >
                <path
                  d="M18.5 36C8.8347 36 1 28.1653 1 18.5V18.5C1 8.83469 8.8347 1 18.5 1C28.1653 1 36 8.83469 36 18.5C36 28.1653 28.1653 36 18.5 36Z"
                  stroke="#141613"
                  strokeWidth="0.43"
                  strokeMiterlimit="10"
                />
                <path
                  d="M16.1345 27.879H19.9087V18.4171H22.5454L22.8241 15.253H19.9087V13.4526C19.9087 12.7068 20.0594 12.413 20.7826 12.413H22.8241V9.12842H20.21C17.4001 9.12842 16.1345 10.3639 16.1345 12.7369V15.2606H14.1683V18.4698H16.1345V27.8865V27.879Z"
                  fill="#141613"
                />
              </svg>
            </div>
          )}
        </div>
      )}

      {props.variant === 'vertical' && (
        <div className={style.socialMediaIconsVertical}>
          <div onClick={shareOnLinkedIn} title="Share via Linkedin">
            <svg
              width="46"
              height="46"
              viewBox="0 0 46 46"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
              role="img"
              aria-label="linkedinshare"
              focusable="false"
            >
              <path
                d="M18.5 36C8.8347 36 1 28.1653 1 18.5C1 8.8347 8.8347 1 18.5 1C28.1653 1 36 8.8347 36 18.5C36 28.1653 28.1653 36 18.5 36Z"
                stroke="#141613"
                strokeWidth="0.43"
                strokeMiterlimit="10"
              />
              <path
                d="M14.2361 15.0273H11.0269V25.4008H14.2361V15.0273Z"
                fill="#141613"
              />
              <path
                d="M12.6165 13.6712C13.6636 13.6712 14.5149 12.8124 14.5149 11.7577C14.5149 10.703 13.6636 9.84424 12.6165 9.84424C11.5693 9.84424 10.718 10.703 10.718 11.7577C10.718 12.8124 11.5693 13.6712 12.6165 13.6712Z"
                fill="#141613"
              />
              <path
                d="M19.3739 19.9615C19.3739 18.5 20.0443 17.6337 21.3325 17.6337C22.5153 17.6337 23.0803 18.4699 23.0803 19.9615V25.4081H26.2744V18.839C26.2744 16.0592 24.7 14.7183 22.5002 14.7183C20.3005 14.7183 19.3739 16.4359 19.3739 16.4359V15.0347H16.2927V25.4081H19.3739V19.9615Z"
                fill="#141613"
              />
            </svg>
          </div>

          <div onClick={shareOnPinterest} title="Share via Pinterest">
            <svg
              width="46"
              height="46"
              viewBox="0 0 46 46"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
              role="img"
              aria-label="pinterestshare"
              focusable="false"
            >
              <g clipPath="url(#clip0_823_228)">
                <path
                  d="M18.5 36.367C8.378 36.367 0.174 28.281 0.174 18.306C0.174 8.331 8.378 0.246 18.5 0.246C28.622 0.246 36.826 8.331 36.826 18.306C36.826 28.281 28.622 36.367 18.5 36.367Z"
                  stroke="#141613"
                  strokeWidth="0.33851063829787237"
                  strokeMiterlimit="10"
                />
                <path
                  d="M13.301 19.791C13.553 19.892 13.782 19.791 13.853 19.519C13.901 19.325 14.027 18.843 14.082 18.641C14.153 18.368 14.129 18.267 13.924 18.034C13.475 17.513 13.191 16.845 13.191 15.888C13.191 13.128 15.289 10.656 18.65 10.656C21.624 10.656 23.265 12.452 23.265 14.846C23.265 17.995 21.853 20.654 19.746 20.654C18.587 20.654 17.719 19.706 17.995 18.547C18.326 17.163 18.973 15.671 18.973 14.668C18.973 13.774 18.484 13.027 17.474 13.027C16.291 13.027 15.336 14.232 15.336 15.857C15.336 16.891 15.691 17.583 15.691 17.583C15.691 17.583 14.477 22.652 14.264 23.539C13.838 25.303 14.2 27.473 14.232 27.69C14.248 27.823 14.421 27.854 14.492 27.753C14.603 27.613 16.015 25.894 16.496 24.176C16.63 23.694 17.277 21.175 17.277 21.175C17.664 21.898 18.792 22.536 19.983 22.536C23.549 22.536 25.963 19.333 25.963 15.049C25.963 11.807 23.178 8.79 18.942 8.79C13.672 8.79 11.021 12.514 11.021 15.616C11.021 17.498 11.747 19.169 13.293 19.791H13.301Z"
                  fill="#141613"
                />
              </g>
              <defs>
                <clipPath id="clip0_823_228">
                  <path
                    width="47"
                    height="46.3184"
                    fill="white"
                    transform="translate(0 0.0947266)"
                    d="M0 0H37V36.463H0V0z"
                  />
                </clipPath>
              </defs>
            </svg>
          </div>

          <div onClick={shareOnTwitter} title="Share via Twitter">
            <svg
              width="46"
              height="46"
              viewBox="0 0 46 46"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
              role="img"
              aria-label="twittershare"
              focusable="false"
            >
              <path
                d="M18.5 36C8.8347 36 1 28.1653 1 18.5V18.5C1 8.83469 8.8347 1 18.5 1C28.1653 1 36 8.83469 36 18.5C36 28.1653 28.1653 36 18.5 36Z"
                stroke="#141613"
                strokeWidth="0.43"
                strokeMiterlimit="10"
              />
              <path
                d="M10.2284 24.4061C11.7275 25.3703 13.5129 25.9278 15.4264 25.9278C21.7243 25.9278 25.28 20.6092 25.0691 15.8331C25.732 15.3585 26.3046 14.7558 26.7641 14.0778C26.1539 14.349 25.4985 14.5298 24.813 14.6127C25.5136 14.1908 26.0484 13.5279 26.3046 12.7369C25.6492 13.1286 24.926 13.4074 24.15 13.558C23.5323 12.8951 22.6509 12.4883 21.6715 12.4883C19.4793 12.4883 17.8747 14.5298 18.3644 16.6542C15.5469 16.5111 13.0458 15.1626 11.3734 13.106C10.4845 14.6278 10.9139 16.6241 12.4206 17.6336C11.8631 17.6185 11.3433 17.4603 10.8838 17.2117C10.8461 18.7862 11.9761 20.2552 13.6033 20.5791C13.1287 20.7072 12.6014 20.7373 12.074 20.6394C12.5034 21.9879 13.7615 22.9672 15.2456 22.9973C13.8218 24.1123 12.0213 24.617 10.2208 24.3985L10.2284 24.4061Z"
                fill="#141613"
              />
            </svg>
          </div>

          <div onClick={openFBPopUp} title="Share via Facebook">
            <svg
              width="46"
              height="46"
              viewBox="0 0 46 46"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
              role="img"
              aria-label="facebookshare"
              focusable="false"
            >
              <path
                d="M18.5 36C8.8347 36 1 28.1653 1 18.5V18.5C1 8.83469 8.8347 1 18.5 1C28.1653 1 36 8.83469 36 18.5C36 28.1653 28.1653 36 18.5 36Z"
                stroke="#141613"
                strokeWidth="0.43"
                strokeMiterlimit="10"
              />
              <path
                d="M16.1345 27.879H19.9087V18.4171H22.5454L22.8241 15.253H19.9087V13.4526C19.9087 12.7068 20.0594 12.413 20.7826 12.413H22.8241V9.12842H20.21C17.4001 9.12842 16.1345 10.3639 16.1345 12.7369V15.2606H14.1683V18.4698H16.1345V27.8865V27.879Z"
                fill="#141613"
              />
            </svg>
          </div>
        </div>
      )}
    </>
  );
};

export default SocialMediaIcons;
