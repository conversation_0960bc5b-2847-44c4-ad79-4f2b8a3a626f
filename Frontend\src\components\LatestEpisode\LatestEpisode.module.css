@value breakpoints: "@styles/breakpoints.module.css";
@value variables: "@styles/variables.module.css";
@value breakpoint-sm-450, breakpoint-md, breakpoint-sm, breakpoint-xl, breakpoint-xl-1440 from breakpoints;
@value colorBlack, colorWhite from variables;

.container {
  display: flex;
  flex-direction: column;
  padding: 80px 300px;
  gap: 40px;

  @media screen and (max-width: breakpoint-xl-1440) {
    padding: 80px 124px;
  }

  @media screen and (max-width: breakpoint-xl) {
    padding: 40px 32px;
  }

  @media screen and (max-width: breakpoint-md) {
    padding: 40px 16px;
  }
}

.title > h2 {
  font-weight: 600;
  font-size: 40px;
  line-height: 56px;
  letter-spacing: -0.8px;
  text-align: center;
}

.subtitle {
  margin-top: 10px;
  text-align: center;
  font-size: 20px;
  line-height: 32px;

  @media screen and (max-width: breakpoint-md) {
    margin-top: 24px;
  }
}

.videoContainer {
  position: relative;
  width: 100%;
  aspect-ratio: 16 / 9;
}

.fromCloud {
  background-color: colorBlack;
  color: colorWhite;
}
