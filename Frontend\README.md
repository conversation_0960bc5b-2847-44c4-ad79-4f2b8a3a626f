# mtl-nextjs-site

New Maruti Techlabs Website v2 — built with **Next.js 14 App Router**, leveraging **Strapi CMS** for content management, and deployed on **Google Cloud Run** using Docker.

## 📦 Tech Stack

- **NPM version**: v10.xx.x
- **Node version**: v20.xx.x
- **Framework**: [Next.js 14](https://nextjs.org/docs)
- **Next.js version**: v14.1.1
- **Styling**: CSS Modules
- **Content Management**: [Strapi CMS](https://strapi.io/)
- **Deployment**: GCP Cloud Run with Docker
- **Search**: [Algolia](https://www.algolia.com/)
- **Forms & Marketing**: [HubSpot](https://www.hubspot.com/)
- **Analytics**: Google Analytics (GA), Google Tag Manager (GTM)

## 📁 Project Structure

This is a **single-repo** project using **App Router**.

- All pages are statically generated by **Next.js**.

## 🧑‍💻 Local Development

### 🔧 Installation

```bash
npm install
# or
yarn
```

### 🏁 Run the app

```bash
npm run dev
# or
yarn dev
```

## 🚀 Production

To build and start the app in production mode:

```bash
npm run build
npm run start
```

## 📚 Content Management

All content is managed via **Strapi** (hosted separately).

### 🔍 Preview Feature

- Preview functionality is enabled through **Strapi**
- Each entry has a "Preview" button that links to the live site for review

.........