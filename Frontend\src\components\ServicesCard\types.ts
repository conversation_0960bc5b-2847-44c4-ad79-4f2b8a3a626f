export interface CardTypes {
  cardContent: string;
  cardTitle: string;
  cardTitle2: string;
  id: number;
  url: string;
  cardImage: {
    data: {
      id: number;
      attributes: {
        alternativeText: string;
        caption: string;
        createdAt: string;
        ext: string;
        formats: null;
        hash: string;
        height: number;
        mime: string;
        name: string;
        previewUrl: null;
        provider: string;
        provider_metadata: null;
        size: number;
        updatedAt: string;
        url: string;
        width: number;
      };
    };
  };
}

export interface ServicesCardTypes {
  id: number;
  title: string;
  subtitle: string;
  ourServicesCard: CardTypes[];
}

/* L2 page card types */
export interface L2ServicesCardTypes {
  id: number;
  title: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  description: any;
  service_page_link: string;
  on_hover_bg_image: {
    data: {
      id: number;
      attributes: {
        alternativeText: string;
        caption: string;
        createdAt: string;
        ext: string;
        formats: null;
        hash: string;
        height: number;
        mime: string;
        name: string;
        previewUrl: null;
        provider: string;
        provider_metadata: null;
        size: number;
        updatedAt: string;
        url: string;
        width: number;
      };
    };
  };
}
export interface L2ServiceType {
  id: number;
  title: string;
  L2ServicesCard: L2ServicesCardTypes[];
}
