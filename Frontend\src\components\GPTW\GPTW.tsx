'use client';

import { Container } from 'react-bootstrap';
import styles from './GPTW.module.css';
import Heading from '@components/Heading';
import Image from 'next/image';
export default function GPTW({ GPTWData }) {
  return (
    <Container fluid className={styles.main_conatiner}>
      <div className={styles.inner_container}>
        <div className={styles.text_container}>
          <Heading
            richTextValue={GPTWData?.title}
            headingType="h2"
            className={styles.title}
          />
          <div
            dangerouslySetInnerHTML={{
              __html: GPTWData.description,
            }}
            className={styles.description}
          />
        </div>
        <div className={styles.image_conatiner}>
          <div className={styles.image_wrapper}>
            <Image
              src={GPTWData?.image?.data?.attributes?.url}
              alt="GPTW"
              width={230}
              height={392}
              className={styles.image}
            />
          </div>
        </div>
      </div>
    </Container>
  );
}
