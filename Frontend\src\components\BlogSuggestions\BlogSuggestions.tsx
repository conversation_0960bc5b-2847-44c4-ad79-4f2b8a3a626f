import { Container } from 'react-bootstrap';
import style from './BlogSuggestions.module.css';
import Image from 'next/image';
import useEmblaCarousel from 'embla-carousel-react';
import Fade from 'embla-carousel-fade';
import Autoplay from 'embla-carousel-autoplay';
import {
  DotButton,
  useDotButton,
} from '@components/HomeHeroSection/CarouselDotButton';
import emblastyles from '../../styles/emlaDots.module.css';
import useMediaQueryState from '@hooks/useMediaQueryState';
import breakpoints from '../../styles/breakpoints.module.css';
import Link from 'next/link';
import classNames from '@utils/classNames';

const BlogSuggestions = ({ blogSuggestionsData }) => {
  if (blogSuggestionsData === null)
    return (
      <h4 style={{ textAlign: 'center', padding: '25px' }}>
        No blog suggestions available.
      </h4>
    );

  blogSuggestionsData = [...blogSuggestionsData?.blogs?.data];

  const [emblaRef, emblaApi] = useEmblaCarousel({ loop: true }, [
    Fade(),
    Autoplay({
      delay: 6000,
      stopOnInteraction: true,
    }),
  ]);

  const { selectedIndex, scrollSnaps, onDotButtonClick } =
    useDotButton(emblaApi);

  const isTablet = useMediaQueryState({
    query: `(max-width: ${breakpoints['breakpoint-md']})`,
  });

  return isTablet ? (
    <section className={style.embla}>
      <div className={style.embla__viewport} ref={emblaRef}>
        <div className={style.embla__container}>
          {blogSuggestionsData.slice(0, 3).map((blogSuggestionsData, index) => (
            <div className={style.embla__slide} key={index}>
              <Link
                href={`/blog/${blogSuggestionsData?.attributes?.slug}`}
                className={classNames(
                  style.box,
                  style.box_additional_mobile_styles,
                )}
              >
                <div className={style.blogDetails}>
                  <Image
                    width={300}
                    height={150}
                    className={style.blogSuggestionsCoverImage}
                    src={
                      blogSuggestionsData?.attributes?.image?.data?.attributes
                        ?.format?.small?.url ||
                      blogSuggestionsData?.attributes?.image?.data?.attributes
                        ?.formats?.small?.url ||
                      blogSuggestionsData?.attributes?.image?.data?.attributes
                        ?.format?.medium?.url ||
                      blogSuggestionsData?.attributes?.image?.data?.attributes
                        ?.formats?.medium?.url ||
                      blogSuggestionsData?.attributes?.image?.data?.attributes
                        ?.format?.large?.url ||
                      blogSuggestionsData?.attributes?.image?.data?.attributes
                        ?.formats?.large?.url ||
                      blogSuggestionsData?.attributes?.image?.data?.attributes
                        ?.url
                    }
                    alt={
                      blogSuggestionsData?.attributes?.image?.data?.attributes
                        ?.alternativeText
                    }
                  />
                  <div className={style.category}>
                    {blogSuggestionsData?.attributes?.type}
                  </div>
                  <div className={style.title}>
                    {blogSuggestionsData?.attributes?.title}
                  </div>
                  <div className={style.description}>
                    {blogSuggestionsData?.attributes?.description}
                  </div>
                </div>
                <div className={style.authorSection}>
                  <Image
                    width={35}
                    height={35}
                    className={style.authorCoverImage}
                    src={
                      blogSuggestionsData?.attributes?.authors?.data[0]
                        ?.attributes?.image?.data[0]?.attributes?.format
                        ?.thumbnail?.url ||
                      blogSuggestionsData?.attributes?.authors?.data[0]
                        ?.attributes?.image?.data[0]?.attributes?.formats
                        ?.thumbnail?.url ||
                      blogSuggestionsData?.attributes?.authors?.data[0]
                        ?.attributes?.image?.data[0]?.attributes?.format?.small
                        ?.url ||
                      blogSuggestionsData?.attributes?.authors?.data[0]
                        ?.attributes?.image?.data[0]?.attributes?.formats?.small
                        ?.url ||
                      blogSuggestionsData?.attributes?.authors?.data[0]
                        ?.attributes?.image?.data[0]?.attributes?.format?.medium
                        ?.url ||
                      blogSuggestionsData?.attributes?.authors?.data[0]
                        ?.attributes?.image?.data[0]?.attributes?.formats
                        ?.medium?.url ||
                      blogSuggestionsData?.attributes?.authors?.data[0]
                        ?.attributes?.image?.data[0]?.attributes?.format?.large
                        ?.url ||
                      blogSuggestionsData?.attributes?.authors?.data[0]
                        ?.attributes?.image?.data[0]?.attributes?.formats?.large
                        ?.url ||
                      blogSuggestionsData?.attributes?.authors?.data[0]
                        ?.attributes?.image?.data[0]?.attributes?.url
                    }
                    alt={
                      blogSuggestionsData?.attributes?.authors?.data[0]
                        ?.attributes?.image?.data[0]?.attributes
                        ?.alternativeText
                    }
                  />
                  <div className={style.author}>
                    <div className={style.authorName}>
                      {
                        blogSuggestionsData?.attributes?.authors?.data[0]
                          ?.attributes?.name
                      }
                    </div>
                    <div className={style.authorDesignation}>
                      {
                        blogSuggestionsData?.attributes?.authors?.data[0]
                          ?.attributes?.designation
                      }
                    </div>
                  </div>
                </div>
              </Link>
            </div>
          ))}
        </div>
      </div>
      <div className={style.embla__controls}>
        <div className={emblastyles.embla__dots}>
          {scrollSnaps.map((_, index) => (
            <DotButton
              key={index}
              onClick={() => onDotButtonClick(index)}
              className={
                index === selectedIndex
                  ? `${emblastyles.embla__dot} ${emblastyles.embla__dot_selected}`
                  : classNames(
                      emblastyles.embla__dot,
                      emblastyles.embla__dot_bg_white,
                    )
              }
            />
          ))}
        </div>
      </div>
    </section>
  ) : (
    <Container className={style.mainContainer} fluid>
      {blogSuggestionsData.slice(0, 3).map((blogSuggestionsData, index) => (
        <Link
          href={`/blog/${blogSuggestionsData?.attributes?.slug}`}
          className={style.box}
          key={index}
        >
          <div className={style.blogDetails}>
            <Image
              width={300}
              height={150}
              className={style.blogSuggestionsCoverImage}
              src={
                blogSuggestionsData?.attributes?.image?.data?.attributes?.format
                  ?.small?.url ||
                blogSuggestionsData?.attributes?.image?.data?.attributes
                  ?.formats?.small?.url ||
                blogSuggestionsData?.attributes?.image?.data?.attributes?.format
                  ?.medium?.url ||
                blogSuggestionsData?.attributes?.image?.data?.attributes
                  ?.formats?.medium?.url ||
                blogSuggestionsData?.attributes?.image?.data?.attributes?.format
                  ?.large?.url ||
                blogSuggestionsData?.attributes?.image?.data?.attributes
                  ?.formats?.large?.url ||
                blogSuggestionsData?.attributes?.image?.data?.attributes?.url
              }
              alt={
                blogSuggestionsData?.attributes?.image?.data?.attributes
                  ?.alternativeText
              }
            />
            <div className={style.category}>
              {blogSuggestionsData?.attributes?.type}
            </div>
            <div className={style.title}>
              {blogSuggestionsData?.attributes?.title}
            </div>
            <div className={style.description}>
              {blogSuggestionsData?.attributes?.description}
            </div>
          </div>
          <div className={style.authorSection}>
            <Image
              width={35}
              height={35}
              className={style.authorCoverImage}
              src={
                blogSuggestionsData?.attributes?.authors?.data[0]?.attributes
                  ?.image?.data[0]?.attributes?.format?.thumbnail?.url ||
                blogSuggestionsData?.attributes?.authors?.data[0]?.attributes
                  ?.image?.data[0]?.attributes?.formats?.thumbnail?.url ||
                blogSuggestionsData?.attributes?.authors?.data[0]?.attributes
                  ?.image?.data[0]?.attributes?.format?.small?.url ||
                blogSuggestionsData?.attributes?.authors?.data[0]?.attributes
                  ?.image?.data[0]?.attributes?.formats?.small?.url ||
                blogSuggestionsData?.attributes?.authors?.data[0]?.attributes
                  ?.image?.data[0]?.attributes?.format?.medium?.url ||
                blogSuggestionsData?.attributes?.authors?.data[0]?.attributes
                  ?.image?.data[0]?.attributes?.formats?.medium?.url ||
                blogSuggestionsData?.attributes?.authors?.data[0]?.attributes
                  ?.image?.data[0]?.attributes?.format?.large?.url ||
                blogSuggestionsData?.attributes?.authors?.data[0]?.attributes
                  ?.image?.data[0]?.attributes?.formats?.large?.url ||
                blogSuggestionsData?.attributes?.authors?.data[0]?.attributes
                  ?.image?.data[0]?.attributes?.url
              }
              alt={
                blogSuggestionsData?.attributes?.authors?.data[0]?.attributes
                  ?.image?.data[0]?.attributes?.alternativeText
              }
            />
            <div className={style.author}>
              <div className={style.authorName}>
                {
                  blogSuggestionsData?.attributes?.authors?.data[0]?.attributes
                    ?.name
                }
              </div>
              <div className={style.authorDesignation}>
                {
                  blogSuggestionsData?.attributes?.authors?.data[0]?.attributes
                    ?.designation
                }
              </div>
            </div>
          </div>
        </Link>
      ))}
    </Container>
  );
};

export default BlogSuggestions;
