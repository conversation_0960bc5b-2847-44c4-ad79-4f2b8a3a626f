'use client';

import style from './CoreValues.module.css';
import Heading from '@components/Heading';
import useEmblaCarousel from 'embla-carousel-react';
import { Container } from 'react-bootstrap';
import usePrevNextButtons from '@hooks/usePrevNextButtons';
import PrevButton from '@components/SliderButtons/PrevButton';
import NextButton from '@components/SliderButtons/NextButton';
import DotButton from '@components/DotButton/DotButton';
import useDotButton from '@hooks/useDotButton';
import emblastyles from '../../styles/emlaDots.module.css';

export default function CoreValues({ coreValuesData }) {
  const [emblaRef, emblaApi] = useEmblaCarousel({
    loop: false,
    dragFree: true,
    skipSnaps: false,
    direction: 'ltr',
  });

  const { selectedIndex, scrollSnaps, onDotButtonClick } =
    useDotButton(emblaApi);

  const {
    prevBtnDisabled,
    nextBtnDisabled,
    onPrevButtonClick,
    onNextButtonClick,
  } = usePrevNextButtons(emblaApi);

  const {
    onPrevButtonClick: onImgPrevButtonClick,
    onNextButtonClick: onImgNextButtonClick,
  } = usePrevNextButtons(emblaApi);

  const onPrev = () => {
    onPrevButtonClick();
    onImgPrevButtonClick();
  };

  const onNext = () => {
    onNextButtonClick();
    onImgNextButtonClick();
  };

  return (
    <Container fluid className={style.sectionWrapper}>
      <div className={style.headerContent}>
        <div className={style.content}>
          <Heading
            className={style.title}
            title={coreValuesData.title}
            headingType="h2"
          />
          <div
            className={style.description}
            dangerouslySetInnerHTML={{
              __html: coreValuesData.description,
            }}
          />
        </div>
        <div className={style.carouselArrowButtons}>
          <PrevButton onClick={onPrev} disabled={prevBtnDisabled} />
          <NextButton onClick={onNext} disabled={nextBtnDisabled} />
        </div>
      </div>
      <section className={style.embla}>
        <div className={style.embla__viewport} ref={emblaRef}>
          <div className={style.embla__container}>
            {coreValuesData?.box?.map((coreValuesBoxData, index) => {
              const bgImage = {
                '--bg-image': `url(${
                  coreValuesBoxData?.image?.data?.attributes?.formats?.small
                    ?.url ||
                  coreValuesBoxData?.image?.data?.attributes?.formats?.medium
                    ?.url ||
                  coreValuesBoxData?.image?.data?.attributes?.formats?.large
                    ?.url ||
                  coreValuesBoxData?.image?.data?.attributes?.url
                })`,
              } as React.CSSProperties;
              return (
                <div className={style.embla__slide} key={index}>
                  <div className={style.embla__slide__number} key={index}>
                    <Heading
                      headingType="h3"
                      className={style.boxTitle}
                      title={coreValuesBoxData?.title}
                    />
                    <div
                      className={style.boxDescription}
                      dangerouslySetInnerHTML={{
                        __html: coreValuesBoxData?.description,
                      }}
                    />
                    <div style={bgImage} className={style.imageWrapper} />
                  </div>
                </div>
              );
            })}
          </div>
        </div>
        <div className={style.embla__dotsContainer}>
          <div className={emblastyles.embla__controls}>
            <div className={emblastyles.embla__dots}>
              {scrollSnaps.map((_, index) => (
                <DotButton
                  key={index}
                  onClick={() => onDotButtonClick(index)}
                  style={
                    index !== selectedIndex ? { background: '#D9D9D9' } : {}
                  }
                  className={
                    index === selectedIndex
                      ? `${emblastyles.embla__dot} ${emblastyles.embla__dot_selected}`
                      : emblastyles.embla__dot
                  }
                />
              ))}
            </div>
          </div>
        </div>
      </section>
    </Container>
  );
}
