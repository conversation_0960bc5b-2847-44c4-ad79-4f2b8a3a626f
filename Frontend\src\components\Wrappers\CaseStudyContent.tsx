'use client';
import React, { useRef } from 'react';
import RichText from '@components/RichText';
import Quote from '@components/Quote';
import CaseStudyForm from '@components/CaseStudyForm';
import CaseStudyHeroSection from '@components/CaseStudyHeroSection';
import RichResults from '@components/RichResults';

const CaseStudyContent = ({ data }) => {
  const formRef = useRef(null);

  const scrollToForm = () => {
    formRef.current.scrollIntoView({
      behavior: 'smooth',
    });
  };
  return (
    <>
      {data?.data[0]?.attributes?.seo && (
        <RichResults data={data?.data[0]?.attributes?.seo} />
      )}
      {data?.data[0]?.attributes?.hero_section && (
        <CaseStudyHeroSection
          heroData={data?.data[0]?.attributes?.hero_section}
          variant={'details_page'}
          scrollToForm={scrollToForm}
        />
      )}
      {data?.data[0]?.attributes?.richText_for_TheClient && (
        <RichText
          richTextData={data?.data[0]?.attributes?.richText_for_TheClient}
          variant="caseStudy"
        />
      )}
      {data?.data[0]?.attributes?.richText_for_Challenges && (
        <RichText
          richTextData={data?.data[0]?.attributes?.richText_for_Challenges}
          variant="caseStudy"
          background={
            data?.data[0]?.attributes?.richText_for_Challenges?.background ===
            'gray'
              ? 'gray'
              : 'black'
          }
          usedFor="challenges"
        />
      )}
      {data?.data[0]?.attributes?.richText_for_Solutions && (
        <RichText
          richTextData={data?.data[0]?.attributes?.richText_for_Solutions}
          variant="caseStudy"
          background={
            data?.data[0]?.attributes?.richText_for_Solutions?.background ===
            'black'
              ? 'black'
              : 'gray'
          }
          usedFor="solutions"
        />
      )}
      {data?.data[0]?.attributes?.quote && (
        <Quote QuoteData={data?.data[0]?.attributes?.quote} />
      )}
      {data?.data[0]?.attributes?.richText_for_Results && (
        <RichText
          richTextData={data?.data[0]?.attributes?.richText_for_Results}
          variant="caseStudy"
          background={
            data?.data[0]?.attributes?.richText_for_Results?.background ===
            'gray'
              ? 'gray'
              : 'black'
          }
          usedFor="results"
        />
      )}
      {data?.data[0]?.attributes?.caseStudy_form && (
        <CaseStudyForm
          formData={data?.data[0]?.attributes?.caseStudy_form}
          ref={formRef}
          source="CaseStudy"
        />
      )}
    </>
  );
};

export default CaseStudyContent;
