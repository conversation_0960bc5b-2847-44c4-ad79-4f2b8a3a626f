import { getConfigValue } from "./ssmConfig.mjs";

/**
 * Send form data to Slack webhook
 * @param formData - The form data to send
 * @param webhookUrl - Optional webhook URL (will use SSM config if not provided)
 * @param extraText - Optional extra text for the message
 */
const sendToSlack = async (
  formData,
  webhookUrl,
  extraText = '',
) => {
  try {
    // If no webhook URL provided, determine which one to use based on extraText
    if (!webhookUrl) {
      if (extraText) {
        // This is a failure notification
        webhookUrl = await getConfigValue('SLACK_FAILURE_WEBHOOK_URL');
      } else {
        // This is a success notification
        webhookUrl = await getConfigValue('SLACK_SUCCESS_WEBHOOK_URL');
      }
    }

    if (!webhookUrl) {
      console.error('Slack webhook URL is not defined.');
      return;
    }

    const isAIReadinessForm = formData.secondary_source === 'AIReadiness';

    const messageTitle = extraText
      ? '🚨 *Form Submission Failed!* ⚠️'
      : isAIReadinessForm
        ? '🤖 *New AI Readiness Submission!* 🤖'
        : '🚀 *New Form Submission!* 🚀';

    const aiFields = `
        • First Name: ${formData.firstName || 'N/A'}
        • Last Name: ${formData.lastName || 'N/A'}
        • Email Address: ${formData.emailAddress || 'N/A'}
        • Phone Number: ${formData.phoneNumber || 'N/A'}
        • Company Name: ${formData.companyName || 'N/A'}
        • UTM Campaign: ${formData.utm_campaign || 'N/A'}
        • UTM Medium: ${formData.utm_medium || 'N/A'}
        • UTM Source: ${formData.utm_source || 'N/A'}
        • GA 4 User ID: ${formData.ga_4_userid || 'N/A'}
        • IP Address: ${formData.ip_address || 'N/A'}
        • City: ${formData.city || 'N/A'}
        • Country: ${formData.country || 'N/A'}
        • Referrer: ${formData.referrer || 'N/A'}
        • Clarity: ${formData.clarity || 'N/A'}
        • Page URL: ${formData.url || 'N/A'}
        • Consent: ${formData.consent ? 'Yes' : 'No'}
        • Do you have clearly defined business objectives and goals for the AI project? : ${formData.do_you_have_clearly_defined_business_objectives_and_goals_for_the_ai_project_ || 'N/A'}
        • How receptive is your Leadership Team to embracing the changes brought about by AI? : ${formData.how_receptive_is_your_leadership_team_to_embracing_the_changes_brought_about_by_ai_ || 'N/A'}
        • Do you have budget allocated for your AI project? : ${formData.do_you_have_budget_allocated_for_your_ai_project_ || 'N/A'}
        • Do you have a robust data infrastructure for storage, retrieval, and processing? : ${formData.do_you_have_a_robust_data_infrastructure_for_storage__retrieval__and_processing_ || 'N/A'}
        • Which of the below DB tools do you currently use? : ${formData.which_of_the_below_db_tools_do_you_currently_use_ || 'N/A'}
        • Is the relevant data for the AI project available and accessible? : ${formData.is_the_relevant_data_for_the_ai_project_available_and_accessible_ || 'N/A'}
        • Do you have access to necessary computing resources (CPU, GPU, cloud services)? : ${formData.do_you_have_access_to_necessary_computing_resources__cpu__gpu__cloud_services__ || 'N/A'}
        • How would you rate your organization's current IT infrastructure in terms of scalability and flexibility to accommodate the evolving computational needs of AI projects? : ${formData.how_would_you_rate_your_organization_s_current_it_infrastructure_in_terms_of_scalability_and_flexib || 'N/A'}
        • Does the team have the expertise in data science, machine learning, and AI? : ${formData.does_the_team_have_the_expertise_in_data_science__machine_learning__and_ai_ || 'N/A'}
        • Do you have systems in place to monitor the performance and accuracy of AI models post-deployment? : ${formData.do_you_have_systems_in_place_to_monitor_the_performance_and_accuracy_of_ai_models_post_deployment_ || 'N/A'}
        • Do you have risk management strategies in place for the AI project? : ${formData.do_you_have_risk_management_strategies_in_place_for_the_ai_project_ || 'N/A'}
        • Do you have a process in place to measure the impact of the deployment of AI / AI-powered solutions? : ${formData.do_you_have_a_process_in_place_to_measure_the_impact_of_the_deployment_of_ai___ai_powered_solutions || 'N/A'}

        • Strategy & Leadership: ${formData.strategy___leadership || '0'} %
        • Data Readiness & Infrastructure: ${formData.data_readiness___infrastructure || '0'} %
        • Talent & Skills: ${formData.talent___skills || 'N/A'} %
        • Execution & Monitoring: ${formData.execution___monitoring || '0'} %
        • Impact Evaluation: ${formData.impact_evaliation || '0'} %
        • Average Score: ${formData.average_of_all_score || '0'} %
        `;

    const contactFields = `
        • *First Name:* ${formData.firstName || 'N/A'}
        • *Last Name:* ${formData.lastName || 'N/A'}
        • *Email:* ${formData.emailAddress || 'N/A'}
        • *Phone:* ${formData.phoneNumber || 'N/A'}
        • *Company:* ${formData.companyName || 'N/A'}
        • *How Did You Hear About Us?* ${formData.howDidYouHearAboutUs || 'N/A'}
        • *How Can We Help?* ${formData.howCanWeHelpYou || 'N/A'}
        • *UTM Campaign:* ${formData.utm_campaign || 'N/A'}
        • *UTM Medium:* ${formData.utm_medium || 'N/A'}
        • *UTM Source:* ${formData.utm_source || 'N/A'}
        • *IP Address:* ${formData.ip_address || 'N/A'}
        • *GA4 User ID:* ${formData.ga_4_userid || 'N/A'}
        • *City:* ${formData.city || 'N/A'}
        • *Country:* ${formData.country || 'N/A'}
        • *Secondary Source:* ${formData.secondary_source || 'N/A'}
        • *Clarity:* ${formData.clarity || 'N/A'}
        • *Page URL:* ${formData.url || 'N/A'}
        • *Referrer:* ${formData.referrer || 'N/A'}
        • *Consent:* ${formData.consent ? 'Yes' : 'No'} `;

    const message = {
      text: messageTitle,
      blocks: [
        {
          type: 'section',
          text: {
            type: 'mrkdwn',
            text: `${messageTitle}\n\n${isAIReadinessForm ? aiFields : contactFields}`,
          },
        },
        { type: 'divider' },
      ],
    };

    const response = await fetch(webhookUrl, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(message),
    });

    if (!response.ok) {
      console.error(
        'Failed to send Slack notification:',
        await response.text(),
      );
    }
  } catch (error) {
    console.error('Error sending message to Slack:', error);
  }
};

export default sendToSlack;
