import React, { useState } from 'react';
import style from './blogTableOfContent.module.css';

const TableOfContent = ({
  blogDataContent,
  onIndexClick,
  isTocVisible = false,
  setTocVisible,
  isThereAnyTitleToShowToc,
  activeDivOnScroll,
}) => {
  const [dropdown, setDropdown] = useState(false);

  const clickHandle = (id, index) => {
    setDropdown(!dropdown);
    onIndexClick(index);
    setTocVisible(false);
  };

  return (
    <>
      {isThereAnyTitleToShowToc && (
        <div className={style.container}>
          <div className={style.toc_title}>Table of contents</div>
          <div className={style.container_div}>
            {blogDataContent.map(
              (data, index) =>
                data.title && (
                  <div
                    className={
                      activeDivOnScroll === data.id
                        ? `${style.scroll_item} ${style.active}`
                        : style.scroll_item
                    }
                    onClick={() => clickHandle(data.id, index)}
                    key={data.id}
                  >
                    <div className={style.title}>{data.title}</div>
                  </div>
                ),
            )}
          </div>
        </div>
      )}

      {isTocVisible && (
        <div className={style.dropdown_menu}>
          {blogDataContent.map(
            (data, index) =>
              data.title && (
                <div
                  className={style.dropdown_menu_div}
                  onClick={() => {
                    clickHandle(data.id, index);
                  }}
                  key={data.id}
                >
                  <div className={style.dropdown_data}>{data.title}</div>
                </div>
              ),
          )}
        </div>
      )}
    </>
  );
};

export default TableOfContent;
