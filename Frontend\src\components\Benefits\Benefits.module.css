@value variables: "@styles/variables.module.css";
@value colorWhite, gray300, colorBlack from variables;
@value breakpoints: "@styles/breakpoints.module.css";
@value breakpoint-sm, breakpoint-md, breakpoint-xl-1440 from breakpoints;

.sectionWrapper {
  margin: 0;
  padding: 0;
  display: flex;
  flex-direction: column;
  gap: 40px;
  background: gray300;
}

.content {
  padding: 5rem 9.375rem 0 9.375rem;
  display: flex;
  flex-direction: column;
  gap: 24px;

  @media (max-width: breakpoint-xl-1440) {
    padding: 5rem 7.75rem 0 7.75rem;
  }

  @media (max-width: breakpoint-md) {
    padding: 2.5rem 2rem 0 2rem;
  }

  @media (max-width: breakpoint-sm) {
    padding: 2.5rem 1rem 0 1rem;
  }
}

.title>h2 {

  color: colorBlack;
  font-size: 40px;
  font-style: normal;
  font-weight: 600;
  line-height: 140%;
  letter-spacing: -0.8px;
  text-align: center;
}

.description {
  color: colorBlack;

  font-size: 20px;
  font-style: normal;
  font-weight: 400;
  line-height: 160%;
  text-align: center;
}

.embla {
  max-width: 100%;
  margin: auto;
  --slide-height: 15.625rem;
  --slide-spacing: 1.25rem;
  --slide-size: auto;
  position: relative;
  pointer-events: none;
}

.embla__viewport {
  overflow: hidden;
}

.embla__container {
  backface-visibility: hidden;
  display: flex;
  touch-action: pan-y pinch-zoom;
  margin-left: calc(var(--slide-spacing) * -1);
}

.embla__slide {
  flex: 0 0 var(--slide-size);
  min-width: 0;
  padding-left: var(--slide-spacing);
  display: flex;
}

.embla__slide__number {
  display: flex;
  height: var(--slide-height);
  position: relative;
}

.image {
  width: fit-content;
  border-radius: 6px;
}

.embla__slide__number::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 250px;
  height: 250px;
  background-color: colorBlack;
  opacity: 0.5;
  border-radius: 6px;
  z-index: 1;
  pointer-events: none;
}

.text_container {
  display: flex;
  flex-direction: column;
  position: absolute;
  max-width: 225px;
  left: 12px;
  top: 25px;
  z-index: 2;
}

.card_title_container {
  min-height: 71px;
}

.card_title {
  color: colorBlack;

  font-size: 16px;
  font-style: normal;
  font-weight: 700;
  line-height: 130%;
  padding: 5px 10px;
  background: colorWhite;
  width: fit-content;
  border-radius: 6px;
}

.card_desc {
  color: colorWhite;

  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}