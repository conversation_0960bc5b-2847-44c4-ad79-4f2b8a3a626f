@value breakpoints: "@styles/breakpoints.module.css";
@value breakpoint-sm, breakpoint-xl from breakpoints;
@value variables: "@styles/variables.module.css";
@value colorBlack, colorWhite from variables;

.container {
  background-color: colorBlack;
  color: colorWhite;

  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: center;
  align-items: flex-start;
  gap: 40px;
  padding: 80px 0;

  background-image: url('https://cdn.marutitech.com/Group_5044_662af9d22b.svg'),
    url('https://cdn.marutitech.com/Group_5043_745de9790c.svg');
  background-repeat: no-repeat, no-repeat;
  background-position:
    52% 3%,
    calc(50% + 560px) 100%;
  background-size: 300px 240px;

  @media screen and (max-width: breakpoint-xl) {
    gap: 30px;
    padding: 40px 32px;

    background-position:
      calc(52% - 260px) 30%,
      calc(50% + 260px) 100%;
  }

  @media screen and (max-width: breakpoint-sm) {
    gap: 30px;
    padding: 40px 16px;

    background-position:
      -40% 40%,
      140% 100%;
  }
}

.left_container {
  position: sticky;
  top: 150px;
  height: auto;

  width: 580px;
  display: flex;
  flex-direction: column;
  gap: 10px;

  @media screen and (max-width: breakpoint-xl) {
    position: static;

    width: 100%;
    text-align: center;
  }
}

.title {
  font-weight: 600;
  font-size: 40px;
  line-height: 140%;
}

.description>p {
  font-weight: 400;
  font-size: 20px;
  line-height: 160%;
  margin: 0;
}

.btn {
  margin-top: 40px;
  width: 165px;
  height: 64px;
  font-size: 20px;

  @media screen and (max-width: breakpoint-xl) {
    margin-left: auto;
    margin-right: auto;
  }
}

.right_container {
  position: relative;
  width: 560px;
  display: flex;
  flex-direction: column;
  gap: 20px;
  z-index: 10;
}

.box_container {
  background-color: #202020;
  padding: 24px;
  display: flex;
  border-radius: 6px;
  flex-direction: column;
  gap: 8px;
}

.box_data_title {
  font-weight: 600;
  font-size: 24px;
  line-height: 138%;
}

.box_data_description>p {
  font-weight: 400;
  font-size: 14px;
  margin: 0;
}