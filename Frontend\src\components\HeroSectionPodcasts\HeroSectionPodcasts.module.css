@value variables: "@styles/variables.module.css";
@value fontWeight600, gray300, fontWeight400 from variables;
@value breakpoints: "@styles/breakpoints.module.css";
@value breakpoint-xl-1440, breakpoint-md, breakpoint-sm from breakpoints;

.containerHeroSection {
  padding: 1rem 9.375rem;
  display: flex;
  gap: 10px;

  @media (max-width: breakpoint-xl-1440) {
    padding: 1rem 1.5rem;
  }

  @media (max-width: breakpoint-md) {
    flex-direction: column-reverse;
  }
}

.content {
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 24px;
  height: 681px;
  width: 100%;
  background: gray300;
  padding: 20px;
  border-radius: 12px;

  @media (max-width: breakpoint-md) {
    height: auto;
  }
}

.title {
  max-width: 935px;
}

.title>h1 {
  font-size: 78px;
  font-weight: fontWeight600;
  line-height: 102%;

  @media (max-width: breakpoint-md) {
    font-size: 48px;
    line-height: 51px;
    letter-spacing: -0.96px;
  }
}

.description {
  max-width: 935px;
  font-size: 20px;
  font-weight: fontWeight400;
  line-height: 160%;

  @media (max-width: breakpoint-md) {
    font-size: 22px;
  }
}

.heroImageContainer {
  width: 100%;
  height: 100%;
}

.heroImage {
  border-radius: 12px;
  width: 100%;

  @media (max-width: breakpoint-md) {
    height: 100%;
  }
}