'use client';

import Image from 'next/image';
import useEmblaCarousel from 'embla-carousel-react';
import styles from './Awards.module.css';
import AutoScroll from 'embla-carousel-auto-scroll';
import { EmblaOptionsType } from 'embla-carousel';
import ImageWithSizing from '@components/ImageWithSizing';

export default function AwardsCarousel({ dataAwards }: any) {
  const OPTIONS: EmblaOptionsType = {
    loop: true,
    align: 'start',
    dragFree: true,
  };
  const [emblaRef] = useEmblaCarousel(OPTIONS, [
    AutoScroll({ playOnInit: true, speed: 0.7 }),
  ]);

  return (
    <div className={styles.embla}>
      <div className={styles.embla__viewport} ref={emblaRef}>
        <div className={styles.embla__container}>
          {dataAwards?.images?.data.map((img: any) => (
            <div key={img?.id} className={styles.embla__slide}>
              <ImageWithSizing
                src={img?.attributes}
                width={100}
                height={100}
                alt={img?.attributes?.alternativeText}
                className={styles.image}
              />
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
