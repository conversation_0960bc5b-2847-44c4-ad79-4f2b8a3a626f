import ContactUs from '@components/ContactUs';
import seoSchema from '@utils/seoSchema';
import fetchFromStrapi from '@utils/fetchFromStrapi';
import RichResults from '@components/RichResults';

async function fetchContactUsData() {
  const queryString =
    'populate=left_section_content.left_side_fields,right_section_content.logo,right_section_content.trusted_by_logos.images,right_section_content.collab_box,right_section_content.request_a_discovery_call_box,right_section_content.our_offices,right_section_content.form_awards.images,seo.schema';
  return await fetchFromStrapi('contact-us', queryString);
}

export async function generateMetadata({}) {
  const queryString =
    'populate=seo&populate=seo.image&populate=seo.metaProperties&populate=seo.keywords&populate=seo.schema';
  const seoFetchedData = await fetchFromStrapi('contact-us', queryString);
  const seoData = seoFetchedData?.data?.attributes?.seo;
  return seoSchema(seoData);
}
export default async function ContactUsPage() {
  const contactUsData = await fetchContactUsData();

  return (
    <>
      {contactUsData?.data?.attributes?.seo && (
        <RichResults data={contactUsData?.data?.attributes?.seo} />
      )}

      {contactUsData?.data?.attributes && (
        <ContactUs
          dataContactUs={contactUsData?.data?.attributes}
          source="ContactUs"
        />
      )}
    </>
  );
}
