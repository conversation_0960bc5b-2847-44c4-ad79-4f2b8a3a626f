# ------------------------
# S3 Bucket
# ------------------------
resource "aws_s3_bucket" "static_site" {
  bucket        = "mtl-site-dev-environment"
  force_destroy = true
  tags          = var.common_tags
}

# Enable S3 Versioning
resource "aws_s3_bucket_versioning" "this" {
  bucket = aws_s3_bucket.static_site.id
  versioning_configuration {
    status = "Enabled"
  }
}

# Enable Static Website Hosting
resource "aws_s3_bucket_website_configuration" "this" {
  bucket = aws_s3_bucket.static_site.id

  index_document {
    suffix = "index.html"
  }

  error_document {
    key = "404.html"
  }
}

# Blocked All Public Access
resource "aws_s3_bucket_public_access_block" "this" {
  bucket = aws_s3_bucket.static_site.id

  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}

# S3 Bucket Policy
resource "aws_s3_bucket_policy" "allow_cloudfront" {
  bucket = aws_s3_bucket.static_site.id
  policy = data.aws_iam_policy_document.allow_cloudfront.json
}

# ------------------------
# S3 Bucket Policy (OAC-based, using SourceArn)
# ------------------------
data "aws_iam_policy_document" "allow_cloudfront" {
  version = "2008-10-17"
  statement {
    sid    = "AllowCloudFrontServicePrincipalReadOnly"
    effect = "Allow"
    principals {
      type        = "Service"
      identifiers = ["cloudfront.amazonaws.com"]
    }
    actions   = ["s3:GetObject"]
    resources = ["${aws_s3_bucket.static_site.arn}/*"]
    condition {
      test     = "StringEquals"
      variable = "AWS:SourceArn"
      values   = ["${aws_cloudfront_distribution.mtl-cloudfront.arn}"]
    }
  }
}

# --------------------------
# CORS Configuration
# --------------------------
resource "aws_s3_bucket_cors_configuration" "static" {
  bucket = aws_s3_bucket.static_site.id

  cors_rule {
    allowed_headers = ["*"]
    allowed_methods = ["GET"]
    allowed_origins = ["*"]
    expose_headers  = []
    max_age_seconds = 3000
  }
}

# --------------------------
# --------------------------
#  maruti-site-cdn bucket for Image Storage
# --------------------------
# --------------------------

resource "aws_s3_bucket" "maruti_site_cdn" {
  bucket        = "maruti-site-cdn"
  force_destroy = true
  tags = {
    Name = "Maruti Site CDN Bucket"
  }
}

# Enable S3 Versioning
resource "aws_s3_bucket_versioning" "versioning_maruti_site_cdn" {
  bucket = aws_s3_bucket.maruti_site_cdn.id

  versioning_configuration {
    status = "Enabled"
  }
}

# Blocked All Public Access
resource "aws_s3_bucket_public_access_block" "block_public_access_maruti_site_cdn" {
  bucket = aws_s3_bucket.maruti_site_cdn.id

  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}

# S3 Bucket Policy
resource "aws_s3_bucket_policy" "allow_cloudfront_maruti_site_cdn" {
  bucket = aws_s3_bucket.maruti_site_cdn.id
  policy = data.aws_iam_policy_document.allow_cloudfront_maruti_site_cdn.json
}

# ------------------------
# S3 Bucket Policy (OAC-based, using SourceArn)
# ------------------------
data "aws_iam_policy_document" "allow_cloudfront_maruti_site_cdn" {
  version = "2008-10-17"
  statement {
    sid    = "AllowCloudFrontServicePrincipal"
    effect = "Allow"
    principals {
      type        = "Service"
      identifiers = ["cloudfront.amazonaws.com"]
    }
    actions   = ["s3:GetObject"]
    resources = ["${aws_s3_bucket.maruti_site_cdn.arn}/*"]
    condition {
      test     = "StringEquals"
      variable = "AWS:SourceArn"
      values   = ["${aws_cloudfront_distribution.cdn.arn}"]
    }
  }
}

# -------------------------- S3 bucket for lambd code 

resource "aws_s3_bucket" "lambda_code_bucket" {
  bucket = "mtl-lambda-code-bucket"
  tags   = var.common_tags
}

