'use client';

import React, { useState } from 'react';
import BlogHeroSection from '@components/BlogHeroSection';
import BlogBody from '@components/BlogBody';
import BlogBanner from '@components/BlogBanner';
import BlogSuggestions from '@components/BlogSuggestions';

export default function BlogDetailsWrapper({ blogData }: { blogData: any }) {
  const propVar = {
    blogData: blogData?.data[0]?.attributes,
    blogSuggestions: blogData?.data[0]?.attributes?.suggestions,
    caseStudySuggestions: blogData?.data[0]?.attributes?.caseStudy_suggestions,
  };

  const [isTocVisible, setTocVisible] = useState(false);
  let isThereAnyTitleToShowToc = false;

  // Check if there is any title to show table of contents
  for (let i = 0; i < propVar.blogData.content.length; i++) {
    if (propVar.blogData.content[i].title != null) {
      isThereAnyTitleToShowToc = true;
    } else {
      isThereAnyTitleToShowToc = false;
    }
  }

  const blogPublisedDateFromStrapi = propVar?.blogData?.publishedAt;
  const blogPublisedDateFromStrapiInUnixTimestamp = new Date(
    blogPublisedDateFromStrapi,
  ).getTime(); // Date in Unix timestamp

  return (
    <>
      <div className="hidden blog-published-date">
        {/* Blog Published Date -> for blog sorting in search page*/}
        {blogPublisedDateFromStrapiInUnixTimestamp}
      </div>
      <BlogHeroSection
        blogData={propVar?.blogData}
        isTocVisible={isTocVisible}
        isThereAnyTitleToShowToc={isThereAnyTitleToShowToc}
        setTocVisible={setTocVisible}
      />
      <BlogBody
        blogData={propVar?.blogData}
        isTocVisible={isTocVisible}
        isThereAnyTitleToShowToc={isThereAnyTitleToShowToc}
        setTocVisible={setTocVisible}
        source="Blog"
      />
      <BlogSuggestions blogSuggestionsData={propVar?.blogSuggestions} />
      <BlogBanner blogCaseStudySuggestionData={propVar?.caseStudySuggestions} />
    </>
  );
}
