@value variables: "@styles/variables.module.css";
@value gray300, colorBlack, colorWhite from variables;
@value breakpoints: "@styles/breakpoints.module.css";
@value breakpoint-xl-1440, breakpoint-lg, breakpoint-md, breakpoint-sm, breakpoint-sm-427 from breakpoints;

.container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 40px;
  padding: 80px 124px;

  @media screen and (max-width: breakpoint-md) {
    padding: 40px 32px;
  }
}

.title>h1 {
  color: colorBlack;

  font-size: 64px;
  font-style: normal;
  font-weight: 600;
  line-height: 120%;
  letter-spacing: -1.28px;
  text-align: center;

  @media screen and (max-width: breakpoint-sm) {
    font-size: 44px;
    letter-spacing: -1.76px;
  }
}

.description {

  font-size: 20px;
  font-style: normal;
  font-weight: 400;
  line-height: 160%;
  text-align: center;
}

.button {
  padding: 16px 36px;
  width: fit-content;

  font-size: 20px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
}