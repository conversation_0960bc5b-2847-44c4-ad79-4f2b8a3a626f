import TabChallenges from './TabChallenges';

export default {
  title: 'Components/Tab Challenges',
};

const data = {
  data: [
    {
      id: 1,
      attributes: {
        createdAt: '2024-08-23T10:05:20.652Z',
        updatedAt: '2024-09-04T12:19:26.579Z',
        publishedAt: '2024-08-23T10:15:37.681Z',
        slug: 'insurance',
        pageName: 'Health Care',
        challenges_and_solutions: {
          id: 1,
          title: 'Challenges in Healthcare Software Development',
          tab_box: [
            {
              id: 1,
              card_title: '01 RESPONSIBILITY',
              challenges_title: 'CHALLENGES YOU FACE',
              challenges_description:
                '<p>Often, mental and social well-being is left unattended while everyone focuses on physical health. However, the importance of the holistic approach for the best health outcomes is a proven fact. How to address the mental and social well-being of your patients?</p>',
              solution_title: 'SOLUTIONS WE OFFER',
              solution_description:
                '<p>Our specialists will conduct a comprehensive discovery stage prior to healthcare app development. Together, we will test your idea and see how it can be improved. We’re always eager to turn your plans into a powerful medical or self-care application.</p>',
              card_image: {
                data: {
                  id: 131,
                  attributes: {
                    name: 'Software Product Engineering final.webp',
                    alternativeText: null,
                    caption: null,
                    width: 384,
                    height: 392,
                    formats: {
                      thumbnail: {
                        name: 'thumbnail_Software Product Engineering final.webp',
                        hash: 'thumbnail_Software_Product_Engineering_final_a7012ba1ba',
                        ext: '.webp',
                        mime: 'image/webp',
                        path: null,
                        width: 153,
                        height: 156,
                        size: 5.03,
                        sizeInBytes: 5032,
                        url: 'https://cdn.marutitech.com/thumbnail_Software_Product_Engineering_final_a7012ba1ba.webp',
                      },
                    },
                    hash: 'Software_Product_Engineering_final_a7012ba1ba',
                    ext: '.webp',
                    mime: 'image/webp',
                    size: 17.94,
                    url: 'https://cdn.marutitech.com/Software_Product_Engineering_final_a7012ba1ba.webp',
                    previewUrl: null,
                    provider:
                      '@strapi-community/strapi-provider-upload-google-cloud-storage',
                    provider_metadata: null,
                    createdAt: '2024-08-14T09:55:51.359Z',
                    updatedAt: '2024-08-14T09:55:51.359Z',
                  },
                },
              },
            },
            {
              id: 2,
              card_title: '02 PATIENT CARE  ETHICS',
              challenges_title: '02 PATIENT CARE  ETHICS',
              challenges_description:
                '<p>Often, mental and social well-being is left unattended while everyone focuses on physical health. However, the importance of the holistic approach for the best health outcomes is a proven fact. How to address the mental and social well-being of your patients?</p>',
              solution_title: 'SOLUTIONS WE OFFER',
              solution_description:
                '<p>Our specialists will conduct a comprehensive discovery stage prior to healthcare app development. Together, we will test your idea and see how it can be improved. We’re always eager to turn your plans into a powerful medical or self-care application.</p>',
              card_image: {
                data: {
                  id: 131,
                  attributes: {
                    name: 'Software Product Engineering final.webp',
                    alternativeText: null,
                    caption: null,
                    width: 384,
                    height: 392,
                    formats: {
                      thumbnail: {
                        name: 'thumbnail_Software Product Engineering final.webp',
                        hash: 'thumbnail_Software_Product_Engineering_final_a7012ba1ba',
                        ext: '.webp',
                        mime: 'image/webp',
                        path: null,
                        width: 153,
                        height: 156,
                        size: 5.03,
                        sizeInBytes: 5032,
                        url: 'https://cdn.marutitech.com/thumbnail_Software_Product_Engineering_final_a7012ba1ba.webp',
                      },
                    },
                    hash: 'Software_Product_Engineering_final_a7012ba1ba',
                    ext: '.webp',
                    mime: 'image/webp',
                    size: 17.94,
                    url: 'https://cdn.marutitech.com/Software_Product_Engineering_final_a7012ba1ba.webp',
                    previewUrl: null,
                    provider:
                      '@strapi-community/strapi-provider-upload-google-cloud-storage',
                    provider_metadata: null,
                    createdAt: '2024-08-14T09:55:51.359Z',
                    updatedAt: '2024-08-14T09:55:51.359Z',
                  },
                },
              },
            },
            {
              id: 3,
              card_title: '03 HOLISTIC  HEALTHCARE',
              challenges_title: '03 HOLISTIC  HEALTHCARE',
              challenges_description:
                '<p>Often, mental and social well-being is left unattended while everyone focuses on physical health. However, the importance of the holistic approach for the best health outcomes is a proven fact. How to address the mental and social well-being of your patients?</p>',
              solution_title: 'SOLUTIONS WE OFFER',
              solution_description:
                '<p>Our specialists will conduct a comprehensive discovery stage prior to healthcare app development. Together, we will test your idea and see how it can be improved. We’re always eager to turn your plans into a powerful medical or self-care application.</p>',
              card_image: {
                data: {
                  id: 131,
                  attributes: {
                    name: 'Software Product Engineering final.webp',
                    alternativeText: null,
                    caption: null,
                    width: 384,
                    height: 392,
                    formats: {
                      thumbnail: {
                        name: 'thumbnail_Software Product Engineering final.webp',
                        hash: 'thumbnail_Software_Product_Engineering_final_a7012ba1ba',
                        ext: '.webp',
                        mime: 'image/webp',
                        path: null,
                        width: 153,
                        height: 156,
                        size: 5.03,
                        sizeInBytes: 5032,
                        url: 'https://cdn.marutitech.com/thumbnail_Software_Product_Engineering_final_a7012ba1ba.webp',
                      },
                    },
                    hash: 'Software_Product_Engineering_final_a7012ba1ba',
                    ext: '.webp',
                    mime: 'image/webp',
                    size: 17.94,
                    url: 'https://cdn.marutitech.com/Software_Product_Engineering_final_a7012ba1ba.webp',
                    previewUrl: null,
                    provider:
                      '@strapi-community/strapi-provider-upload-google-cloud-storage',
                    provider_metadata: null,
                    createdAt: '2024-08-14T09:55:51.359Z',
                    updatedAt: '2024-08-14T09:55:51.359Z',
                  },
                },
              },
            },
            {
              id: 4,
              card_title: '04 STAYING  RELEVANT',
              challenges_title: '04 STAYING  RELEVANT',
              challenges_description:
                '<p>Often, mental and social well-being is left unattended while everyone focuses on physical health. However, the importance of the holistic approach for the best health outcomes is a proven fact. How to address the mental and social well-being of your patients?</p>',
              solution_title: 'SOLUTIONS WE OFFER',
              solution_description:
                '<p>Our specialists will conduct a comprehensive discovery stage prior to healthcare app development. Together, we will test your idea and see how it can be improved. We’re always eager to turn your plans into a powerful medical or self-care application.</p>',
              card_image: {
                data: {
                  id: 131,
                  attributes: {
                    name: 'Software Product Engineering final.webp',
                    alternativeText: null,
                    caption: null,
                    width: 384,
                    height: 392,
                    formats: {
                      thumbnail: {
                        name: 'thumbnail_Software Product Engineering final.webp',
                        hash: 'thumbnail_Software_Product_Engineering_final_a7012ba1ba',
                        ext: '.webp',
                        mime: 'image/webp',
                        path: null,
                        width: 153,
                        height: 156,
                        size: 5.03,
                        sizeInBytes: 5032,
                        url: 'https://cdn.marutitech.com/thumbnail_Software_Product_Engineering_final_a7012ba1ba.webp',
                      },
                    },
                    hash: 'Software_Product_Engineering_final_a7012ba1ba',
                    ext: '.webp',
                    mime: 'image/webp',
                    size: 17.94,
                    url: 'https://cdn.marutitech.com/Software_Product_Engineering_final_a7012ba1ba.webp',
                    previewUrl: null,
                    provider:
                      '@strapi-community/strapi-provider-upload-google-cloud-storage',
                    provider_metadata: null,
                    createdAt: '2024-08-14T09:55:51.359Z',
                    updatedAt: '2024-08-14T09:55:51.359Z',
                  },
                },
              },
            },
            {
              id: 5,
              card_title: '05 RESPONSIBILITY',
              challenges_title: '05 RESPONSIBILITY',
              challenges_description:
                '<p>Often, mental and social well-being is left unattended while everyone focuses on physical health. However, the importance of the holistic approach for the best health outcomes is a proven fact. How to address the mental and social well-being of your patients?</p>',
              solution_title: 'SOLUTIONS WE OFFER',
              solution_description:
                '<p>Our specialists will conduct a comprehensive discovery stage prior to healthcare app development. Together, we will test your idea and see how it can be improved. We’re always eager to turn your plans into a powerful medical or self-care application.</p>',
              card_image: {
                data: {
                  id: 131,
                  attributes: {
                    name: 'Software Product Engineering final.webp',
                    alternativeText: null,
                    caption: null,
                    width: 384,
                    height: 392,
                    formats: {
                      thumbnail: {
                        name: 'thumbnail_Software Product Engineering final.webp',
                        hash: 'thumbnail_Software_Product_Engineering_final_a7012ba1ba',
                        ext: '.webp',
                        mime: 'image/webp',
                        path: null,
                        width: 153,
                        height: 156,
                        size: 5.03,
                        sizeInBytes: 5032,
                        url: 'https://cdn.marutitech.com/thumbnail_Software_Product_Engineering_final_a7012ba1ba.webp',
                      },
                    },
                    hash: 'Software_Product_Engineering_final_a7012ba1ba',
                    ext: '.webp',
                    mime: 'image/webp',
                    size: 17.94,
                    url: 'https://cdn.marutitech.com/Software_Product_Engineering_final_a7012ba1ba.webp',
                    previewUrl: null,
                    provider:
                      '@strapi-community/strapi-provider-upload-google-cloud-storage',
                    provider_metadata: null,
                    createdAt: '2024-08-14T09:55:51.359Z',
                    updatedAt: '2024-08-14T09:55:51.359Z',
                  },
                },
              },
            },
            {
              id: 6,
              card_title: '06 RESPONSIBILITY',
              challenges_title: '06 RESPONSIBILITY',
              challenges_description:
                '<p>Often, mental and social well-being is left unattended while everyone focuses on physical health. However, the importance of the holistic approach for the best health outcomes is a proven fact. How to address the mental and social well-being of your patients?</p>',
              solution_title: 'SOLUTIONS WE OFFER',
              solution_description:
                '<p>Our specialists will conduct a comprehensive discovery stage prior to healthcare app development. Together, we will test your idea and see how it can be improved. We’re always eager to turn your plans into a powerful medical or self-care application.</p>',
              card_image: {
                data: {
                  id: 131,
                  attributes: {
                    name: 'Software Product Engineering final.webp',
                    alternativeText: null,
                    caption: null,
                    width: 384,
                    height: 392,
                    formats: {
                      thumbnail: {
                        name: 'thumbnail_Software Product Engineering final.webp',
                        hash: 'thumbnail_Software_Product_Engineering_final_a7012ba1ba',
                        ext: '.webp',
                        mime: 'image/webp',
                        path: null,
                        width: 153,
                        height: 156,
                        size: 5.03,
                        sizeInBytes: 5032,
                        url: 'https://cdn.marutitech.com/thumbnail_Software_Product_Engineering_final_a7012ba1ba.webp',
                      },
                    },
                    hash: 'Software_Product_Engineering_final_a7012ba1ba',
                    ext: '.webp',
                    mime: 'image/webp',
                    size: 17.94,
                    url: 'https://cdn.marutitech.com/Software_Product_Engineering_final_a7012ba1ba.webp',
                    previewUrl: null,
                    provider:
                      '@strapi-community/strapi-provider-upload-google-cloud-storage',
                    provider_metadata: null,
                    createdAt: '2024-08-14T09:55:51.359Z',
                    updatedAt: '2024-08-14T09:55:51.359Z',
                  },
                },
              },
            },
          ],
        },
      },
    },
  ],
};

export function TabChallenegsStory() {
  return (
    <>
      <TabChallenges
        tabChallengesData={data?.data[0]?.attributes?.challenges_and_solutions}
        variant="industry"
      />
    </>
  );
}
