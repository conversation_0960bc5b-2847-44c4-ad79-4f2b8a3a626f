'use client';

import style from './Benefits.module.css';
import Heading from '@components/Heading';
import Image from 'next/image';
import { EmblaOptionsType } from 'embla-carousel';
import useEmblaCarousel from 'embla-carousel-react';
import AutoScroll from 'embla-carousel-auto-scroll';
import { Container } from 'react-bootstrap';

export default function Benefits({ BenefitsData }) {
  const OPTIONS: EmblaOptionsType = {
    loop: true,
    dragFree: false,
    skipSnaps: false,
  };

  // First slider
  const [emblaRef1] = useEmblaCarousel(OPTIONS, [
    AutoScroll({
      playOnInit: true,
      speed: 1,
      direction: 'backward',
      stopOnFocusIn: false,
      stopOnMouseEnter: false,
      stopOnInteraction: false,
    }),
  ]);

  // Second slider
  const [emblaRef2] = useEmblaCarousel(OPTIONS, [
    AutoScroll({
      playOnInit: true,
      speed: 1,
      direction: 'forward',
      stopOnFocusIn: false,
      stopOnMouseEnter: false,
      stopOnInteraction: false,
    }),
  ]);

  return (
    <Container fluid className={style.sectionWrapper}>
      <div className={style.content}>
        <Heading
          className={style.title}
          title={BenefitsData.title}
          headingType="h2"
        />
        <div
          className={style.description}
          dangerouslySetInnerHTML={{
            __html: BenefitsData.description,
          }}
        />
      </div>

      {/* First Slider */}
      <div className={style.embla}>
        <div className={style.embla__viewport} ref={emblaRef1}>
          <div className={style.embla__container}>
            {BenefitsData?.first_slider?.map((data, index) => (
              <div className={style.embla__slide} key={index}>
                <div className={style.embla__slide__number} key={index}>
                  <Image
                    className={style.image}
                    src={
                      data?.image?.data?.attributes?.format?.small?.url ||
                      data?.image?.data?.attributes?.formats?.small?.url ||
                      data?.image?.data?.attributes?.format?.medium?.url ||
                      data?.image?.data?.attributes?.formats?.medium?.url ||
                      data?.image?.data?.attributes?.format?.large?.url ||
                      data?.image?.data?.attributes?.formats?.large?.url ||
                      data?.image?.data?.attributes?.url
                    }
                    alt={data?.image?.data?.attributes?.alternativeText}
                    width={250}
                    height={250}
                  />
                  <div className={style.text_container}>
                    <div className={style.card_title_container}>
                      <div className={style.card_title}>{data?.title}</div>
                    </div>
                    <div
                      className={style.card_desc}
                      dangerouslySetInnerHTML={{ __html: data?.description }}
                    ></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Second Slider */}
      <div className={style.embla}>
        <div className={style.embla__viewport} ref={emblaRef2}>
          <div className={style.embla__container}>
            {BenefitsData?.second_slider?.map((data, index) => (
              <div className={style.embla__slide} key={index}>
                <div className={style.embla__slide__number} key={index}>
                  <Image
                    className={style.image}
                    src={
                      data?.image?.data?.attributes?.format?.small?.url ||
                      data?.image?.data?.attributes?.formats?.small?.url ||
                      data?.image?.data?.attributes?.format?.medium?.url ||
                      data?.image?.data?.attributes?.formats?.medium?.url ||
                      data?.image?.data?.attributes?.format?.large?.url ||
                      data?.image?.data?.attributes?.formats?.large?.url ||
                      data?.image?.data?.attributes?.url
                    }
                    alt={data?.image?.data?.attributes?.alternativeText}
                    width={250}
                    height={250}
                  />
                  <div className={style.text_container}>
                    <div className={style.card_title_container}>
                      <div className={style.card_title}>{data?.title}</div>
                    </div>
                    <div
                      className={style.card_desc}
                      dangerouslySetInnerHTML={{ __html: data?.description }}
                    ></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </Container>
  );
}
