import React from 'react';
import seoSchema from '@utils/seoSchema';
import HeroSection from '@components/HeroSection';
import TrustedPartners from '@components/TrustedPartners';
import IndustriesCard from '@components/IndustriesCard';
import ServicesCard from '@components/ServicesCard';
import CTA from '@components/CTA';
import MeetOurTeam from '@components/MeetOurTeam';
import CaseStudyCard from '@components/CaseStudyCard';
import Insights from '@components/Insights';
import NewsAndEvents from '@components/NewsAndEvents';
import { notFound } from 'next/navigation';
import ContactUsForm from '@components/ContactUsForm';
import RichResults from '@components/RichResults';
import fetchFromStrapi from '@utils/fetchFromStrapi';

export async function generateStaticParams() {
  const partnersPageResponse = await fetchFromStrapi('partners');
  const partnerSlug = partnersPageResponse.data.map(res => ({
    partnerSlug: res.attributes.slug,
  }));

  return partnerSlug;
}

export async function fetchPartnersData(slug: string) {
  const queryString = `filters[slug][$eq]=${slug}&populate=hero_section.image,Industries.industriesCardsBox.backgroundImage,Industries.industriesCardsBox.button,service_offering.L2ServicesCard.on_hover_bg_image,cta,meet_our_people.our_people.logo,meet_our_people.our_people.image,news_and_events.news.image,news_and_events.event_main_pages.hero_section,cta_other,case_study_cards.case_study_relation.preview.preview_background_image&populate=case_study_cards.case_study_relation.hero_section.global_services,insights.circular_text_image,insights.blogs.heroSection_image,seo.schema`;
  return await fetchFromStrapi('partners', queryString);
}

async function getTrustedPartnersData() {
  const queryString =
    'populate=trustedPartner.title&populate=trustedPartner.partnersLogo.images';
  return await fetchFromStrapi('trusted-partner', queryString);
}

async function getFormData() {
  const queryString = 'populate=form.formFields&populate=form.button';
  return await fetchFromStrapi('form', queryString);
}

export async function generateMetadata({
  params,
}: {
  params: { partnerSlug: string };
}) {
  const { partnerSlug: slug } = params;
  const queryString = `filters[slug][$eq]=${slug}&populate=seo&populate=seo.image&populate=seo.metaProperties&populate=seo.keywords,seo.schema`;
  const seoFetchedData = await fetchFromStrapi('partners', queryString);
  const seoData = seoFetchedData?.data[0]?.attributes?.seo;

  return seoSchema(seoData);
}

export default async function Partners({ params }: { params: any }) {
  const { partnerSlug } = params;
  const partnersData = await fetchPartnersData(partnerSlug);
  const trustedPartner = await getTrustedPartnersData();
  const formData = await getFormData();

  // Check if partners page data exists, otherwise return 404
  if (!partnersData?.data || partnersData?.data.length === 0) {
    notFound();
  }
  return (
    <>
      {partnersData?.data[0]?.attributes?.seo && (
        <RichResults data={partnersData?.data[0]?.attributes?.seo} />
      )}
      {partnersData?.data[0]?.attributes?.hero_section && (
        <HeroSection
          heroData={partnersData?.data[0]?.attributes?.hero_section}
          variant="partners"
        />
      )}

      {trustedPartner?.data?.attributes?.trustedPartner && (
        <TrustedPartners
          data={trustedPartner?.data?.attributes?.trustedPartner}
        />
      )}

      {partnersData?.data[0]?.attributes?.Industries && (
        <IndustriesCard
          industriesCardData={partnersData?.data[0]?.attributes?.Industries}
        />
      )}

      {partnersData?.data[0]?.attributes?.service_offering && (
        <ServicesCard
          variant="whiteSlideCard"
          l2ServiceData={partnersData?.data[0]?.attributes?.service_offering}
        />
      )}

      {partnersData?.data[0]?.attributes?.cta && (
        <CTA data={partnersData?.data[0]?.attributes?.cta} />
      )}

      {partnersData?.data[0]?.attributes?.meet_our_people && (
        <MeetOurTeam
          meetOurTeamData={partnersData?.data[0]?.attributes?.meet_our_people}
          variant="partners"
        />
      )}
      {partnersData?.data[0]?.attributes?.news_and_events && (
        <NewsAndEvents
          newsEventsData={partnersData?.data[0]?.attributes?.news_and_events}
        />
      )}
      {partnersData?.data[0]?.attributes?.cta_other && (
        <CTA
          data={partnersData?.data[0]?.attributes?.cta_other}
          variant="scrollToContactForm"
        />
      )}

      {partnersData?.data[0]?.attributes?.case_study_cards && (
        <CaseStudyCard
          case_study={partnersData?.data[0]?.attributes?.case_study_cards}
        />
      )}

      {partnersData?.data[0]?.attributes?.insights && (
        <Insights data={partnersData?.data[0]?.attributes?.insights} />
      )}
      {formData?.data?.attributes?.form && (
        <ContactUsForm
          formData={formData?.data?.attributes?.form}
          source="Partners"
        />
      )}
    </>
  );
}
