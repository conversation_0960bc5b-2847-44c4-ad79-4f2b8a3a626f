@value variables: "@styles/variables.module.css";
@value fontWeight600, colorBlack, colorWhite, brandColorOne, brandColorTwo, brandColorThree, brandColorFour, brandColorFive, brandColorThree, fifteenSpace, grayBorder, gray300 from variables;
@value breakpoints: "@styles/breakpoints.module.css";
@value breakpoint-xl-1440, breakpoint-xl-1024, breakpoint-xl, breakpoint-lg, breakpoint-md, breakpoint-sm, breakpoint-sm-427 from breakpoints;

.container {
  margin: 0;
  padding: 80px 120px;
  background-color: #f3f3f3;

  @media screen and (max-width: breakpoint-md) {
    padding: 40px 32px;
  }

  @media screen and (max-width: breakpoint-sm-550) {
    padding: 40px 0;
  }
}

.title {
  margin-bottom: 2.5rem;
}

.title>h2 {
  font-weight: fontWeight600;
  font-size: 40px;
  line-height: 56px;

  @media screen and (max-width: breakpoint-sm) {
    font-size: 28px;
    line-height: 38.64px;
  }
}

.embla__viewport {
  overflow: hidden;
}

.embla__container {
  display: flex;
}

.embla__slide {
  margin-left: 20px;
}

.embla__controls {
  display: grid;
  justify-content: center;
  gap: 1.2rem;
  margin-top: 40px;
}

.imageContainer {
  display: flex;
  justify-content: center;
  gap: 20px;
}

.imageWrapper>img {
  width: 350px;
  height: 439px;

  @media (max-width: breakpoint-xl) {
    width: 300px;
    height: auto;
  }
}