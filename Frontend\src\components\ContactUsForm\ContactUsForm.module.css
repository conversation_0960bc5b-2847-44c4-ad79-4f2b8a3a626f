@value variables: "@styles/variables.module.css";
@value colorBlack, colorWhite, gray, brandColorOne, brandColorTwo, brandColorThree, brandColorFour, brandColorFive from variables;
@value breakpoints: "@styles/breakpoints.module.css";
@value breakpoint-md, breakpoint-md-767, breakpoint-sm, breakpoint-xl-1024, breakpoint-xl, breakpoint-xl-1400 from breakpoints;

.ContactUsFormContainer {
  margin: 0;
  padding: 0;
  position: relative;
  color: colorWhite;
  background-color: colorBlack;
}

.backgroundImage {
  object-fit: scale-down;
  object-position: bottom right;
}

.formWrapper {
  position: relative;
  z-index: 10;
  padding: 5rem 8rem;
  display: flex;
  flex-direction: column;
  gap: 2.5rem;

  @media (max-width: breakpoint-xl-1024) {
    padding: 2.5rem 2rem;
  }
}

.formHeader {
  display: flex;
  flex-direction: column;
  gap: 8px;

  @media (max-width: breakpoint-sm) {
    text-align: center;
  }
}

.formTitle h3 {

  font-size: 40px;
  font-style: normal;
  font-weight: 600;
  line-height: 140%;
  letter-spacing: -0.8px;

  @media (max-width: breakpoint-sm) {
    font-size: 28px;
    font-style: normal;
    font-weight: 600;
    line-height: 138%;
    letter-spacing: -0.84px;
  }
}

.formInstructions {

  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}

.form {
  width: 66.66666666666666666%;
  display: flex;
  flex-direction: column;
  gap: 2.5rem;

  @media (max-width: breakpoint-md) {
    width: 100%;
  }

  @media (min-width: breakpoint-md) and (max-width: breakpoint-xl-1024) {
    width: 85%;
  }

  @media (min-width: breakpoint-xl-1400) {
    width: 793px;
  }
}

.formFields {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.personalDetailsWrapper {
  display: flex;
  gap: 24px;
  flex-direction: column;

  @media (min-width: breakpoint-sm) and (max-width: breakpoint-md) {
    width: 66.6%;
  }

  @media (min-width: breakpoint-md) and (max-width: breakpoint-xl-1024) {
    width: 66.6%;
  }
}

.row {
  display: flex;
  gap: 20px;
}

.nameAndInputWrapper {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.width25 {
  width: 23.5%;
}

.width47andHalf {
  width: 47.5%;
}

.width48andHalf {
  width: 48.5%;
}

.width100 {
  width: 100%;
}

.firstRow {
  flex-direction: row;

  @media (max-width: breakpoint-xl-1024) {
    flex-direction: column !important;
    gap: 24px;
  }
}

.nameFields {
  @media (max-width: breakpoint-md) {
    width: 100%;
  }

  @media (min-width: breakpoint-md) and (max-width: breakpoint-xl-1024) {
    width: 100%;
  }
}

.emailIdField {
  @media (max-width: breakpoint-md) {
    width: 100%;
  }

  @media (min-width: breakpoint-md) and (max-width: breakpoint-xl-1024) {
    width: 100%;
  }
}

.secondRow {
  flex-direction: row;

  @media (max-width: breakpoint-md) {
    flex-direction: column;
    gap: 24px;
  }

  @media (min-width: breakpoint-md) and (max-width: breakpoint-xl-1024) {
    flex-direction: column;
    gap: 24px;
  }
}

.companyNameWrapper {
  @media (max-width: breakpoint-xl-1024) {
    width: 100%;
  }
}

.phoneNumberWrapper {
  @media (max-width: breakpoint-xl-1024) {
    width: 100%;
  }
}

.formLabel {

  font-size: 16px;
  font-style: normal;
  font-weight: 500;
  line-height: 160%;
}

.formInput {
  background: gray;
  color: colorWhite;
  border-radius: 3px;
  height: 41px;
  border: none;
  padding: 10px;
}

.formInputPhone {
  background: gray !important;
  color: colorWhite !important;
  height: 41px !important;
  width: 100% !important;
  border: none !important;
  padding: 10px;
  box-shadow: none !important;
}

.formInputPhone_dial_icon {
  background: gray !important;
  color: colorWhite !important;
  height: 41px !important;
  padding: 10px;
  border: none !important;
  box-shadow: none !important;
}

.ph_number_countries_dropdown {
  color: colorBlack !important;
}

.formInputForHowCanWeHelpYou {
  height: 82px;
  max-height: 250px;
}

.formInput:focus-visible {
  border: 0;
  margin: 0;
}

.consentRow {
  gap: 12px;
  justify-content: normal;
}

.consentText {
  user-select: none;
  cursor: pointer;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}

.submitButtonRow {
  align-items: center;
  justify-content: start;
  gap: 24px;

  @media (max-width: breakpoint-md-767) {
    flex-direction: column;
    gap: 24px;
  }
}

.submitButton {
  padding: 16px 36px !important;

  @media (max-width: breakpoint-md-767) {
    width: 100%;
  }
}

.submitButton>div {
  font-size: 20px;
  font-style: normal;
  font-weight: 600;
  line-height: 160%;
}

.submitButton::before {
  border-radius: 6px;
  padding: 2px;
}

.linkedInButton {
  display: flex;
  gap: 8px;
  align-items: center;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  text-decoration: none;
}

.errorInput {
  border: 1px solid #ff0000 !important;
}

.errorMessages {
  font-weight: 500;
  font-size: 16px;
  line-height: 25.6px;
  color: #ff0000;
}

.errorLabel {
  color: #ff0000;
}

.container_spinner {
  position: relative;
  width: 170px;
  height: 68px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 3px;
  border: 2px solid transparent;
  cursor: pointer;
  background-image: linear-gradient(colorBlack, colorBlack),
    linear-gradient(93.12deg,
      brandColorOne 0%,
      brandColorTwo 30.56%,
      brandColorThree 53.47%,
      brandColorFour 75.75%,
      brandColorFive 100%);
  background-origin: border-box;
  background-clip: padding-box, border-box;
  z-index: 1;
}

.spinner {
  width: 35px;
  height: 35px;
  border-radius: 50%;
  background: linear-gradient(93deg,
      #febe10 0%,
      #f47a37 30.56%,
      #f05443 53.47%,
      #d91a5f 75.75%,
      #b41f5e 100%);

  -webkit-mask-image: radial-gradient(circle,
      rgba(0, 0, 0, 0) 55%,
      rgba(0, 0, 0, 1) 60%);
  animation: spin 0.8s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}