import axios from "axios";
import { getConfigValue } from "./ssmConfig.mjs";

/**
 * Send form data to HubSpot
 * @param formPage - The form page identifier
 * @param payload - The form data payload
 * @param formGuid - The HubSpot form GUID
 * @returns Promise with status and message/error
 */
const sendDataToHubspot = async (formPage, payload, formGuid) => {
  try {
    // Get HubSpot Portal ID from SSM configuration
    const hubspotPortalId = await getConfigValue('HUBSPOT_PORTAL_ID');

    const response = await axios.post(
      `https://api.hsforms.com/submissions/v3/integration/submit/${hubspotPortalId}/${formGuid}`,
      payload
    );

    return {
      status: response.status, // Return actual HTTP status code
      message: `${formPage} form data sent to HubSpot successfully.`,
    };
  } catch (error) {
    return {
      status: error.response?.status || 500, // Return error status if available
      error: `${formPage} error while sending data to HubSpot.`,
    };
  }
};

export default sendDataToHubspot;
