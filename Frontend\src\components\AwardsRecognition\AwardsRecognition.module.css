@value variables: "@styles/variables.module.css";
@value colorBlack, colorWhite, fifteenSpace, grayBorder, brandColorOne, brandColorTwo, brandColorThree, brandColorFour, brandColorFive, bodyTextXXXSSmall from variables;
@value breakpoints: "@styles/breakpoints.module.css";
@value breakpoint-md, breakpoint-xl-2000, breakpoint-xl-1800, breakpoint-sm-450, breakpoint-sm-550, breakpoint-sm-320, breakpoint-xl-1024, breakpoint-xl-1440, breakpoint-lg, breakpoint-lg-991px from breakpoints;

.main_container {
  background: colorBlack;
  padding: 80px 124px;

  @media screen and (max-width: 1200px) {
    padding: 80px 60px;
  }

  @media screen and (max-width: breakpoint-md) {
    padding: 40px 32px;
  }
}

.inner_container {
  display: flex;
  gap: 40px;
  flex-direction: column;
  user-select: none;
}

.main_title h2 {
  color: colorWhite;

  font-size: 40px;
  font-style: normal;
  font-weight: 600;
  line-height: 56px;
  text-align: center;

  @media screen and (max-width: breakpoint-sm-450) {
    font-size: 28px;
    font-style: normal;
    font-weight: 600;
    line-height: 138%;
  }
}

.card {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.card_box {
  display: flex;
  justify-content: space-between;
}

.image_box {
  width: 182px;
  height: 182px;
  display: flex;
  justify-content: center;
  align-items: center;
  background: colorWhite;
  border-radius: 6px;
}

.line {
  width: 67px;
  height: 4px;
  background: linear-gradient(
    93deg,
    #febe10 0%,
    #f47a37 30.56%,
    #f05443 53.47%,
    #d91a5f 75.75%,
    #b41f5e 100%
  );

  border-radius: 2px;
  margin-top: 16px;
}

.awards_title h3 {
  font-size: 20px;
  font-style: normal;
  font-weight: 400;
  line-height: 160%;
  color: colorWhite;
  text-align: center;
  width: 244px;
  padding: 0 7px;
}

.embla {
  @media screen and (max-width: 400px) {
    padding: 0 33px;
  }

  @media screen and (min-width: 401px) and (max-width: breakpoint-sm-450) {
    padding: 0 54px;
  }

  @media screen and (min-width: breakpoint-sm-550) and (max-width: breakpoint-md) {
    padding: 0 110px;
  }
}

.embla__viewport {
  overflow: hidden;
  @media screen and (min-width: breakpoint-xl-2000) {
    justify-self: center;
  }
}

.embla__container {
  display: flex;

  @media screen and (min-width: breakpoint-xl-2000) {
    justify-content: center;
  }
}

.embla__slide {
  display: flex;
}
