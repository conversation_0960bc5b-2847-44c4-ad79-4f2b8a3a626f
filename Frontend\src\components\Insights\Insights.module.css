@value breakpoints: "@styles/breakpoints.module.css";
@value breakpoint-md-769, breakpoint-sm-550, breakpoint-xl-1051, breakpoint-xl, breakpoint-sm, breakpoint-md from breakpoints;

.insightContainer {
  padding-left: 0px;
  padding-right: 0px;
}

.insightSection {
  margin: 80px 0px;
}

.insightsHeadingArea {
  display: flex;
  justify-content: space-between;
  align-items: center;

  @media (min-width: 1441px) {
    margin: 0 150px;
  }

  @media (max-width: 1441px) {
    margin: 0 124px;
  }

  @media (max-width: breakpoint-xl-1051) {
    margin: 40px 32px;
  }

  @media (max-width: breakpoint-md-769) {
    margin: 40px 32px;
  }

  @media (max-width: breakpoint-sm-550) {
    text-align: center;
    flex-direction: column;
    justify-content: center;
  }
}

.titleAndSubtitle h2 {
  font-weight: 600;
  font-size: 40px;
  line-height: 56px;

  @media screen and (max-width: breakpoint-sm) {
    font-size: 28px;
    line-height: 38.64px;
  }
}

.titleAndSubtitle {
  max-width: 100%;
  width: 50%;
  color: black;

  @media (max-width: breakpoint-xl-1051) {
    width: 75%;
  }

  @media (max-width: breakpoint-sm-550) {
    width: 100%;
  }
}

.subtitle {
  margin-top: 24px;
  display: block;
  font-size: 20px;

  @media (max-width: breakpoint-sm-550) {
    margin-bottom: 24px;
  }
}

.viewMoreLink {
  padding-top: 24px;
  display: block;
}

.insightsSlider {
  margin: 40px 0px;
  display: flex;
  align-items: center;

  @media (max-width: breakpoint-xl-1051) {
    flex-direction: column;
  }
}

.sliderImage {
  max-height: 822px;
  position: relative;

  @media (max-width: 1441px) {
    height: 454px;
  }

  @media (max-width: breakpoint-sm-550) {
    height: 250px;
    width: 416px;
  }

  @media (min-width: 1442px) {
    height: 822px;
  }
}

.image {
  width: 100%;
  height: 100%;
  border: 0;
  border-radius: 0px 6px 6px 0px;
  position: relative !important;
  object-fit: cover;

  @media (max-width: breakpoint-xl-1051) {
    border-radius: 6px;
  }
}

.sliderInfo {
  width: 50%;
  padding-left: 80px;
  padding-right: 90px;

  @media (max-width: breakpoint-xl) {
    padding-left: 40px;
    padding-right: 40px;
  }

  @media (max-width: breakpoint-xl-1051) {
    padding-left: 32px;
    padding-right: 32px;
    width: 100%;
  }
}

.embla {
  max-width: 50%;
  margin: auto;
  --slide-height: 19rem;
  --slide-spacing: 1rem;
  --slide-size: 100%;
  z-index: 2;

  @media (max-width: breakpoint-xl-1051) {
    max-width: 92%;
    --slide-spacing: 2rem;
  }
}

.embla__viewport {
  border: 0;
  overflow-x: hidden;

  @media (max-width: breakpoint-xl-1051) {
    border-radius: 6px;
  }
}

.embla__container {
  backface-visibility: hidden;
  display: flex;
  touch-action: pan-y pinch-zoom;
  margin-left: calc(var(--slide-spacing) * -1);
  margin-right: calc(var(--slide-spacing) * -1);
}

.embla__slide {
  flex: 0 0 var(--slide-size);
  min-width: 542px;
  padding-left: var(--slide-spacing);

  @media (max-width: breakpoint-xl-1051) {
    min-width: 384px;
  }
}

.embla_texts {
  max-width: 100%;
  margin: auto;
  --slide-height: 19rem;
  --slide-text-spacing: 2rem;
  --slide-text-size: 100%;
}

.embla__viewport__texts {
  overflow-x: hidden;
  z-index: 1;
}

.embla__container__texts {
  backface-visibility: hidden;
  display: flex;
  touch-action: pan-y pinch-zoom;
}

.embla__slide__texts h3 {
  font-weight: 600;
  font-size: 32px;
  line-height: 42.24px;

  @media screen and (max-width: breakpoint-md) {
    font-size: 40px;
    line-height: 56px;
  }

  @media screen and (max-width: breakpoint-sm) {
    font-size: 28px;
    line-height: 38.64px;
  }
}

.embla__slide__texts {
  flex: 0 0 var(--slide-text-size);
  min-width: 0;
  padding-left: var(--slide-text-spacingg);

  @media (max-width: breakpoint-xl-1051) {
    padding-top: 40px;
  }

  @media (max-width: breakpoint-sm-550) {
    text-align: center;
  }
}

.embla__controls {
  display: grid;
  grid-template-columns: auto 1fr;
  justify-content: space-between;
  gap: 1.2rem;
  margin-top: 40px;

  @media (max-width: breakpoint-md-769) {
    justify-content: center;
    grid-template-columns: none;
    padding-left: 0;
  }
}

.embla__buttons {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
