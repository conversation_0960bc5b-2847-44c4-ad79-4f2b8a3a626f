import CaseStudyHeroSection from '@components/CaseStudyHeroSection';
import EbooksListing from '@components/EbooksListing';
import seoSchema from '@utils/seoSchema';
import fetchFromStrapi from '@utils/fetchFromStrapi';
import RichResults from '@components/RichResults';

async function fetchEbooksPageData() {
  const queryString =
    'populate=hero_section.image,ebooks_cards.image,seo.schema';
  return await fetchFromStrapi('e-books-listing-page', queryString);
}

async function fetchEbooksListingData() {
  const queryString = 'populate=preview.preview_image';
  return await fetchFromStrapi('e-books', queryString);
}

export async function generateMetadata({}) {
  const queryString =
    'populate=seo&populate=seo.image&populate=seo.metaProperties&populate=seo.keywords&populate=seo.schema';
  const seoFetchedData = await fetchFromStrapi(
    'e-books-listing-page',
    queryString,
  );
  const seoData = seoFetchedData?.data?.attributes?.seo;
  return seoSchema(seoData);
}

export default async function Ebooks() {
  const ebooksPageData = await fetchEbooksPageData();
  const ebooksLIstingData = await fetchEbooksListingData();

  return (
    <>
      {ebooksPageData?.data?.attributes?.seo && (
        <RichResults data={ebooksPageData?.data?.attributes?.seo} />
      )}
      {ebooksPageData?.data?.attributes?.hero_section && (
        <CaseStudyHeroSection
          heroData={ebooksPageData?.data?.attributes?.hero_section}
          variant={'listing_page'}
        />
      )}
      {ebooksLIstingData?.data && (
        <EbooksListing
          slug={ebooksLIstingData?.data?.attributes?.slug}
          boxData={ebooksLIstingData?.data}
        />
      )}
    </>
  );
}
