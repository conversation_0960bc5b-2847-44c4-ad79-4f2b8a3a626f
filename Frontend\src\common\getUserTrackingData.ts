declare global {
  interface Window {
    clarity?: (action: string, param: string) => string;
  }
}
const getUserTrackingData = async () => {
  try {
    // Fetch referrer (previous page)
    const referrer = document.referrer || '';

    // Fetch URL parameters
    const urlParams = new URLSearchParams(window.location.search);
    const utm_medium = urlParams.get('utm_medium') || '';
    const utm_source = urlParams.get('utm_source') || '';
    const utm_campaign = urlParams.get('utm_campaign') || '';

    // Get Clarity Session ID
    let clarity = '';
    if (window.clarity) {
      try {
        clarity = window.clarity('get', 'userId');
      } catch (error) {
        console.error('Error fetching Clarity ID:', error);
      }
    }

    // Get GA4 Client ID
    let gaClientId = '';
    try {
      const gaGlobalVid = globalThis.gaGlobal?.vid.match(/\d+\.\d+$/)?.[0];
      gaClientId = gaGlobalVid || ''; // If gaGlobalVid is not found, assign an empty string
    } catch (error) {
      console.error('Error fetching GA4 Client ID:', error);
    }

    return {
      clarity,
      utm_medium,
      utm_source,
      utm_campaign,
      referrer,
      ga_client_id: gaClientId,
    };
  } catch (error) {
    console.error('Error fetching user tracking data:', error);
    return {
      clarity: '',
      utm_medium: '',
      utm_source: '',
      utm_campaign: '',
      referrer: '',
      ga_client_id: '',
    };
  }
};

export default getUserTrackingData;
