'use client';

import { useState } from 'react';
import styles from './YTVideo.module.css';
import Image from 'next/image';

export default function YTVideo({
  embedLink,
  thumbnailUrl,
  playButtonUrl,
}: {
  embedLink: string;
  thumbnailUrl: string;
  playButtonUrl: string;
}) {
  const [playing, setPlaying] = useState(false);

  return (
    <>
      {playing ? (
        <iframe
          src={`${embedLink}?autoplay=1&enablejsapi=1&rel=0`}
          title="YouTube video player"
          allow="autoplay; encrypted-media; picture-in-picture"
          allowFullScreen
          className={styles.iframe}
        ></iframe>
      ) : (
        <>
          <Image
            src={playButtonUrl}
            width={65}
            height={65}
            alt="play button"
            className={styles.playButton}
            onClick={() => setPlaying(true)}
          />
          <Image
            src={thumbnailUrl}
            fill
            alt="thumbnail"
            className={styles.thumbnail}
            onClick={() => setPlaying(true)}
          />
        </>
      )}
    </>
  );
}
