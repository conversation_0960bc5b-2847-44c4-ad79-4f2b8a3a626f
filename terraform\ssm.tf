
# resource "aws_ssm_parameter" "my_app_parameters" {
#   for_each    = var.ssm_parameters
#   name        = "/maruti_site/env/${each.key}"
#   type        = "SecureString"
#   key_id      = "alias/aws/ssm"
#   value       = each.value
#   description = "Application parameter for ${each.key}"
#   overwrite   = true
#   tags        = var.common_tags
# }


# resource "aws_ssm_parameter" "ssm_parameters" {
#   name        = "/maruti_site/env"  # desired parameter name
#   type        = "SecureString"         # or "SecureString" for sensitive data
#   value       = jsonencode(var.ssm_parameters)
#   overwrite   = true
#   description = "Map stored as JSON in a single SSM parameter"
#   tags = var.common_tags
# }
# resource "aws_ssm_parameter" "cf_custom_auth" {
#   name        = "/auth/mtl-cf-custom-auth"
#   type        = "SecureString"
#   key_id      = "alias/aws/ssm"
#   value       = var.mtl-cf-custom-auth
#   description = "CloudFront → API Gateway custom auth token"
#   overwrite   = true
#   tags        = var.common_tags
# }