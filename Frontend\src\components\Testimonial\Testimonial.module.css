@value variables: "@styles/variables.module.css";
@value colorBlack, colorWhite, fifteenSpace, grayBorder, brandColorOne, brandColorTwo, brandColorThree, brandColorFour, brandColorFive, bodyTextXXXSSmall from variables;
@value breakpoints: "@styles/breakpoints.module.css";
@value breakpoint-md, breakpoint-sm-427, breakpoint-xl-1024, breakpoint-xl-1440, breakpoint-xl-2100, breakpoint-md-769, breakpoint-sm-430, breakpoint-sm-390, breakpoint-sm-326, breakpoint-md-850, breakpoint-xl-2559, breakpoint-sm-450, breakpoint-sm from breakpoints;

.testimonialWrapper {
  background-color: colorBlack;
  color: colorWhite;
  padding: 80px 150px;

  @media (max-width: breakpoint-xl-1440) {
    padding: 80px 124px;
  }

  @media screen and (max-width: breakpoint-md) {
    padding: 80px 32px;
  }

  @media screen and (max-width: breakpoint-sm-427) {
    padding: 40px 16px;
  }

  overflow: hidden;
}

.sliderContainer {
  display: flex;
  position: relative;
}

.slide {
  margin: 0 10px;
  position: relative;
  cursor: pointer;
}

.img {
  border-radius: 10px;

  @media (max-width: breakpoint-sm-326) {
    width: 300px !important;
  }

  @media (max-width: breakpoint-sm-390) {
    width: 350px;
    height: 230px;
  }
}

.clientTestiMonial {
  display: flex;
  align-items: center;
  position: absolute;
  bottom: 28px;
  left: 24px;
}

.clientInfo {
  margin-left: 8px;
}

.clientName h3 {
  font-weight: 600;
  font-size: 24px;
  line-height: 33.12px;
  letter-spacing: 0.48px;
}

.clientDesignation {
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}

.clientDesignation > p {
  margin: 0;
  padding: 0;
}

.testimonialTagline {
  padding-top: 10px;

  @media (max-width: breakpoint-md-850) {
    text-align: center;
    padding: 25px 0px;
  }
}

.testimonialSection {
  display: flex;
  justify-content: center;
  gap: 30px;
  align-items: center;

  @media (max-width: breakpoint-md-850) {
    flex-direction: column;
  }
}

.testimonialHeading {
  margin-right: 15px;

  @media (max-width: breakpoint-md-769) {
    margin-right: 0;
  }
}

.title>h2 {
  font-weight: 600;
  font-size: 40px;
  line-height: 56px;

  @media (max-width: breakpoint-md-769) {
    text-align: center;
  }

  @media screen and (max-width: breakpoint-sm) {
    font-size: 28px;
    line-height: 38.64px;
  }
}

.emblaWrapper {
  position: relative;
  z-index: 2;
}

.bottomLeftGradient {
  position: absolute;
  bottom: -20%;
  left: -13%;
  z-index: -1;

  @media (min-width: breakpoint-xl-2559) {
    bottom: -20%;
    left: -6%;
  }

  @media (max-width: breakpoint-md-769) {
    bottom: -8%;
    left: -18%;
  }

  @media (max-width: breakpoint-sm-450) {
    bottom: 0%;
    left: 0%;
  }

  @media (max-width: breakpoint-sm-390) {
    bottom: -10%;
    left: -13%;
  }
}

.topRightGradient {
  position: absolute;
  top: -20%;
  right: -13%;
  z-index: -1;

  @media (min-width: breakpoint-xl-2559) {
    bottom: -20%;
    right: -6%;
  }

  @media (max-width: breakpoint-sm-450) {
    bottom: -20%;
    right: 0%;
  }

  @media (max-width: breakpoint-sm-390) {
    top: -20%;
    right: -13%;
  }
}

.embla {
  --slide-height: 19rem;
  /* --slide-spacing: 1rem; */
  /* --slide-size: 44%; */

  @media (max-width: breakpoint-sm-326) {
    width: 300px !important;
    --slide-size: 0.5% !important;
  }

  @media (max-width: breakpoint-sm-390) {
    width: 400px !important;
    --slide-size: 87.5% !important;
  }

  @media screen and (max-width: 1243px) {
    width: 657px;
    --slide-size: 60%;
  }

  @media screen and (min-width: 1244px) and (max-width: breakpoint-xl-2100) {
    width: 875px;
    --slide-size: 44%;
  }

  @media screen and (min-width: 2100px) and (max-width: 2420px) {
    width: 1451px;
    --slide-size: 27%;
  }
}

.embla__viewport {
  overflow: hidden;
}

.embla__container {
  backface-visibility: hidden;
  display: flex;
  touch-action: pan-y pinch-zoom;
  /* margin-left: calc(var(--slide-spacing) * -1); */
}

.embla__slide {
  flex: 0 0 var(--slide-size);
  min-width: 0;
  /* padding-left: var(--slide-spacing); */

  @media (max-width: breakpoint-sm-326) {
    flex: none;
  }
}

.embla__slide__number {
  box-shadow: inset 0 0 0 0.2rem var(--detail-medium-contrast);
  border-radius: 1.8rem;
  font-size: 4rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  height: var(--slide-height);
}

.embla__controls {
  display: grid;
  grid-template-columns: auto 1fr;
  justify-content: space-between;
  gap: 1.2rem;
  margin-top: 40px;
}

.mobileDots {
  /* display: none;
  @media (max-width: breakpoint-md-850) {
    display: block;
  } */
  display: block;
}

.desktopDots {
  display: none;

  @media (min-width: breakpoint-xl-1024) {
    display: block;
  }

  @media (max-width: breakpoint-md) {
    display: none;
  }
}