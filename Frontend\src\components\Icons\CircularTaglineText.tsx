import React from 'react';

export default function CircularTaglineText({
  textColor = 'white',
  width = '70',
  height = '70',
}: {
  textColor?: string;
  width?: string;
  height?: string;
}) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
      viewBox="0 0 71 70"
      fill="none"
    >
      <path
        d="M21.0834 2.78985L22.6261 2.27562C23.346 2.0185 23.9116 1.96708 24.2716 2.06993C24.683 2.17277 24.9401 2.42989 25.0944 2.8927C25.1972 3.20123 25.1972 3.45835 25.1458 3.71547C25.043 3.97258 24.8887 4.17827 24.5801 4.33254V4.38397C24.9915 4.33254 25.3001 4.38397 25.5058 4.53824C25.7629 4.69251 25.9172 4.94962 26.0714 5.25816C26.2257 5.72097 26.1743 6.13235 25.9686 6.54374C25.7115 6.9037 25.3515 7.21224 24.7344 7.41793L22.8318 7.98359L21.0834 2.78985ZM22.6261 4.64108L23.4488 4.38397C23.8088 4.28112 24.0659 4.12685 24.1688 3.97258C24.2716 3.81831 24.323 3.5612 24.2202 3.30408C24.1173 3.04696 23.9631 2.8927 23.7574 2.84127C23.5517 2.78985 23.2431 2.84127 22.8832 2.94412L22.1633 3.20123L22.6261 4.64108ZM22.8832 5.36101L23.4488 7.00655L24.3745 6.69801C24.7344 6.59516 24.9915 6.44089 25.0944 6.2352C25.2486 6.02951 25.2486 5.77239 25.1458 5.51528C25.043 5.25816 24.8887 5.05247 24.683 5.00104C24.4773 4.94962 24.1688 4.94962 23.7574 5.10389L22.8832 5.36101Z"
        fill={textColor}
      />
      <path
        d="M31.8311 5.6696L28.7457 6.08099L27.9744 0.630135L31.0598 0.21875L31.1626 0.990097L29.0028 1.29864L29.2085 2.78991L31.2654 2.48137L31.3683 3.25272L29.3114 3.56125L29.5685 5.25822L31.7797 4.94968L31.8311 5.6696Z"
        fill={textColor}
      />
      <path
        d="M36.4074 0.733028C35.8932 0.733028 35.4818 0.887298 35.1732 1.24726C34.8647 1.60722 34.7104 2.12145 34.659 2.73853C34.6076 3.40703 34.7619 3.92126 35.019 4.28122C35.2761 4.64119 35.6875 4.84688 36.2531 4.84688C36.5102 4.84688 36.7159 4.84688 36.9216 4.79546C37.1273 4.74403 37.3844 4.69261 37.5901 4.64119L37.5387 5.41253C37.1273 5.5668 36.6131 5.66965 36.0989 5.61823C35.2761 5.5668 34.7104 5.30969 34.2991 4.79546C33.8877 4.28122 33.7334 3.5613 33.7334 2.68711C33.7848 2.12145 33.8877 1.60722 34.0934 1.19584C34.2991 0.784452 34.659 0.475913 35.0704 0.27022C35.4818 0.0645275 35.9446 -0.0383188 36.4588 0.0131044C37.0245 0.0645275 37.5387 0.167374 38.0015 0.42449L37.6416 1.14441C37.4359 1.04157 37.2816 0.938721 37.0759 0.887298C36.8702 0.784451 36.6645 0.733028 36.4074 0.733028Z"
        fill={textColor}
      />
      <path
        d="M43.0929 6.64666L42.9387 5.05254L40.8818 4.53831L39.9561 5.87531L39.0305 5.6182L42.4244 0.784424L43.3501 1.04154L43.9671 6.90378L43.0929 6.64666ZM42.8873 4.17835L42.7844 2.63566C42.7844 2.53281 42.7844 2.37854 42.733 2.12143C42.733 1.91573 42.733 1.71004 42.733 1.60719C42.5787 1.86431 42.4244 2.17285 42.1673 2.53281L41.3446 3.81839L42.8873 4.17835Z"
        fill={textColor}
      />
      <path
        d="M52.1947 4.48686L50.5491 7.62367C50.3434 7.98363 50.1377 8.24075 49.8292 8.44644C49.5207 8.65213 49.2121 8.70355 48.8522 8.70355C48.4922 8.70355 48.0808 8.60071 47.6694 8.39502C47.0524 8.08648 46.6924 7.67509 46.4867 7.16086C46.3324 6.64663 46.3839 6.1324 46.6924 5.56674L48.3379 2.42993L49.1607 2.84132L47.5666 5.92671C47.3609 6.33809 47.3095 6.69805 47.3609 6.95517C47.4637 7.26371 47.6694 7.4694 48.0808 7.67509C48.8522 8.08648 49.4178 7.88078 49.8292 7.05801L51.4233 3.97263L52.1947 4.48686Z"
        fill={textColor}
      />
      <path
        d="M55.3316 11.6862C55.023 12.0461 54.663 12.2518 54.2002 12.2518C53.7374 12.2518 53.2746 12.0461 52.8118 11.6347C52.349 11.2234 51.989 10.8634 51.8348 10.452L52.349 9.7835C52.4519 10.0406 52.6061 10.2977 52.8118 10.5034C52.9661 10.7605 53.1718 10.9148 53.3775 11.0691C53.6346 11.2748 53.8917 11.429 54.0974 11.429C54.3031 11.429 54.5088 11.3262 54.6631 11.1719C54.8173 11.0177 54.8687 10.812 54.8173 10.6063C54.7659 10.4006 54.6631 10.0406 54.4059 9.57781C54.1488 9.115 53.9946 8.70362 54.046 8.34366C54.046 8.03512 54.1488 7.72658 54.4059 7.41804C54.7145 7.05808 55.0744 6.90381 55.4858 6.90381C55.8972 6.90381 56.3086 7.05808 56.7714 7.41804C57.1828 7.72658 57.4913 8.18939 57.7484 8.70362L57.0799 9.115C56.8228 8.6522 56.5657 8.29223 56.2572 8.03512C56.0515 7.82943 55.8458 7.778 55.6401 7.778C55.4344 7.778 55.2801 7.88085 55.1259 8.03512C55.023 8.13796 54.9716 8.24081 54.9716 8.39508C54.9716 8.49793 54.9716 8.6522 55.023 8.80647C55.0744 8.96073 55.1773 9.21785 55.383 9.57781C55.5887 9.9892 55.7429 10.2977 55.7944 10.5034C55.8458 10.7605 55.8458 10.9662 55.7944 11.1719C55.5887 11.3262 55.4858 11.5319 55.3316 11.6862Z"
        fill={textColor}
      />
      <path
        d="M57.8511 16.417L55.7942 14.0515L59.9595 10.4519L62.0164 12.8174L61.4507 13.3316L60.0109 11.6346L58.8796 12.6117L60.2166 14.1544L59.6509 14.6686L58.3139 13.1259L57.0283 14.2572L58.4682 15.9542L57.8511 16.417Z"
        fill={textColor}
      />
      <path
        d="M63.6107 19.7081L66.4389 19.7595L66.9017 20.6337L63.045 20.5309L61.1423 21.5079L60.731 20.6851L62.6336 19.7595L64.8448 16.5713L65.3076 17.4455L63.6107 19.7081Z"
        fill={textColor}
      />
      <path
        d="M66.5931 28.6559C65.7189 28.8616 64.999 28.8102 64.3819 28.5016C63.8163 28.1931 63.4049 27.6274 63.1992 26.8561C62.9935 26.0333 63.0963 25.3648 63.4563 24.7992C63.8163 24.2335 64.4333 23.8735 65.359 23.6678C66.2846 23.4622 66.9531 23.5136 67.5702 23.8221C68.1358 24.1307 68.5472 24.6963 68.7529 25.5191C68.9586 26.2904 68.8557 27.0104 68.4958 27.5246C68.1358 28.0388 67.4673 28.4502 66.5931 28.6559ZM65.6161 24.542C64.9476 24.6963 64.4848 24.9534 64.2277 25.3134C63.9705 25.6734 63.8677 26.0847 64.022 26.599C64.1762 27.1132 64.4333 27.4732 64.8447 27.6274C65.2561 27.8331 65.7704 27.8331 66.4389 27.6274C67.1074 27.4732 67.5702 27.216 67.8273 26.8561C68.0844 26.4961 68.1872 26.0847 68.033 25.5705C67.8787 25.0563 67.6216 24.6963 67.2102 24.4906C66.7988 24.3878 66.2332 24.3878 65.6161 24.542Z"
        fill={textColor}
      />
      <path
        d="M69.9356 36.3692L66.3874 36.3178C65.976 36.3178 65.616 36.2149 65.3075 36.0092C64.9989 35.8549 64.7932 35.5978 64.639 35.2379C64.4847 34.9293 64.4333 34.5179 64.4333 34.0551C64.4333 33.3866 64.639 32.821 64.9989 32.461C65.3589 32.1011 65.8731 31.8954 66.4902 31.9468L70.0384 31.9982V32.9238L66.5416 32.8724C66.0788 32.8724 65.7703 32.9752 65.5132 33.1809C65.3075 33.3866 65.1532 33.6952 65.1532 34.158C65.1532 35.0322 65.5646 35.4436 66.4902 35.4436L69.987 35.495L69.9356 36.3692Z"
        fill={textColor}
      />
      <path
        d="M66.9532 40.5346L66.7989 41.1517C66.6961 41.5631 66.6961 41.8716 66.8504 42.0773C66.9532 42.283 67.1589 42.4373 67.4674 42.4887C67.776 42.5401 68.0331 42.4887 68.1873 42.3344C68.3416 42.1802 68.4959 41.8716 68.5987 41.4603L68.753 40.8946L66.9532 40.5346ZM66.2333 40.3289L64.0735 39.8661L64.2792 38.9919L69.6272 40.2261L69.2672 41.7688C69.113 42.4373 68.8559 42.9515 68.5473 43.2086C68.1874 43.4658 67.776 43.5686 67.2617 43.4658C66.5932 43.3115 66.1819 42.8487 66.079 42.0773L63.3536 43.0544L63.5593 42.0773L65.9762 41.2546L66.2333 40.3289Z"
        fill={textColor}
      />
      <path
        d="M63.7133 50.0993L62.7363 51.8477L60.3194 50.5107C60.3708 50.1507 60.4737 49.8422 60.5765 49.5851C60.6794 49.2765 60.8336 49.0194 60.9879 48.7109C61.3993 47.9909 61.9135 47.5281 62.582 47.3739C63.2505 47.2196 63.9704 47.3739 64.7418 47.7853C65.5131 48.1966 65.9759 48.7623 66.1816 49.4822C66.3873 50.1507 66.2331 50.9221 65.8217 51.6934C65.5646 52.2076 65.1532 52.619 64.7418 52.979L64.2276 52.3619C64.6389 52.0534 64.9475 51.7448 65.1532 51.3334C65.4617 50.8192 65.5131 50.305 65.3589 49.8422C65.2046 49.3794 64.8446 48.968 64.279 48.6594C63.7133 48.3509 63.1477 48.1966 62.7363 48.2995C62.2735 48.4023 61.9135 48.7109 61.605 49.2251C61.4507 49.4822 61.3479 49.7908 61.245 50.0993L62.4792 50.7678L63.0448 49.7908L63.7133 50.0993Z"
        fill={textColor}
      />
      <path
        d="M60.3194 55.293L59.908 55.7558C59.6509 56.0643 59.4966 56.3729 59.4966 56.5786C59.4966 56.8357 59.5995 57.0414 59.8052 57.2471C60.0623 57.4528 60.268 57.5042 60.5251 57.4528C60.7822 57.4013 61.0393 57.1956 61.2964 56.8871L61.7078 56.4243L60.3194 55.293ZM59.7537 54.7788L58.1082 53.3389L58.7253 52.6704L62.8391 56.3215L61.8107 57.5042C61.3479 58.0184 60.885 58.327 60.4737 58.4298C60.0623 58.5326 59.6509 58.3784 59.2395 58.0184C58.7253 57.5556 58.6224 56.99 58.8795 56.27L56.0513 55.8072L56.7198 55.0359L59.2395 55.4987L59.7537 54.7788Z"
        fill={textColor}
      />
      <path
        d="M52.3488 62.2352C51.8346 61.5153 51.6289 60.7953 51.6803 60.1268C51.7832 59.4583 52.1431 58.8927 52.8116 58.4299C53.4801 57.9671 54.1486 57.7614 54.8171 57.9156C55.4856 58.0699 56.0513 58.5327 56.5655 59.2526C57.0798 59.9726 57.2855 60.6925 57.234 61.361C57.1312 62.0295 56.7712 62.5951 56.1027 63.058C55.4342 63.5208 54.7657 63.675 54.1486 63.5722C53.5316 63.4693 52.8631 62.9551 52.3488 62.2352ZM55.7428 59.8183C55.3314 59.2526 54.92 58.9441 54.5086 58.7898C54.0972 58.687 53.6344 58.7384 53.223 59.0469C52.8116 59.3555 52.5545 59.7154 52.5545 60.1783C52.5545 60.6411 52.7088 61.1039 53.1202 61.6695C53.5316 62.2352 53.8915 62.5437 54.3543 62.698C54.7657 62.8523 55.2285 62.7494 55.6399 62.4409C56.1027 62.1323 56.3084 61.7724 56.3598 61.3096C56.3084 60.8468 56.1541 60.384 55.7428 59.8183Z"
        fill={textColor}
      />
      <path
        d="M43.6583 63.1607L44.5839 62.8522L46.538 65.6805C46.6409 65.7833 46.7437 65.989 46.898 66.1947C47.0523 66.4518 47.1551 66.6061 47.2065 66.7089C47.1551 66.5547 47.1551 66.349 47.1037 66.0919C47.0523 65.8347 47.0523 65.629 47.0523 65.5262L46.898 62.1323L47.8236 61.8237L49.3663 64.1892L50.909 66.5547L50.0348 66.8632L48.2864 64.0864C48.0293 63.6235 47.7722 63.2122 47.6179 62.9036C47.6693 63.1093 47.7208 63.3664 47.7208 63.6235C47.7722 63.8807 47.7722 64.0864 47.7722 64.292L47.9264 67.6345L47.1037 67.9431L45.201 65.1662C45.0467 64.9091 44.7896 64.4977 44.5325 63.9835C44.6354 64.3435 44.6868 64.8063 44.7382 65.3719L44.9953 68.663L44.1211 68.9716L43.6583 63.1607Z"
        fill={textColor}
      />
      <path
        d="M37.796 64.2921L38.6702 64.2407L39.0816 68.9717L40.6757 68.8174L40.7271 69.5887L36.6133 69.9487L36.5618 69.1773L38.1559 69.0231L37.796 64.2921Z"
        fill={textColor}
      />
      <path
        d="M30.1855 64.0349L31.0597 64.1378L30.7511 66.6061L33.2709 66.9146L33.5794 64.4463L34.4536 64.5491L33.7851 70L32.9109 69.8971L33.168 67.686L30.6483 67.3774L30.3912 69.5886L29.517 69.4858L30.1855 64.0349Z"
        fill={textColor}
      />
      <path
        d="M21.3922 66.4517C21.855 66.6574 22.3178 66.6574 22.7292 66.4517C23.1406 66.246 23.5006 65.8346 23.7577 65.2175C24.0148 64.6005 24.1176 64.0862 23.9634 63.6234C23.8605 63.212 23.552 62.8521 23.0378 62.6464C22.8321 62.5435 22.6264 62.4921 22.3693 62.4407C22.1636 62.3893 21.9064 62.3378 21.6493 62.3378L21.9579 61.6179C22.4207 61.6179 22.8835 61.7722 23.3977 61.9779C24.1176 62.2864 24.5805 62.7492 24.7861 63.4177C24.9918 64.0348 24.889 64.7547 24.529 65.5775C24.3233 66.0917 24.0148 66.5031 23.6548 66.8117C23.2949 67.1202 22.8835 67.2745 22.4207 67.3259C21.9579 67.3773 21.4951 67.2745 20.9808 67.0688C20.4666 66.8631 20.0552 66.5031 19.6953 66.0917L20.3123 65.5261C20.4666 65.6803 20.6209 65.8346 20.7751 65.9889C20.9808 66.246 21.1865 66.3488 21.3922 66.4517Z"
        fill={textColor}
      />
      <path
        d="M17.3296 58.5327L16.8668 60.0754L18.5638 61.3095L19.9008 60.3839L20.6721 60.9496L15.7869 64.2407L15.0156 63.675L16.5583 57.967L17.3296 58.5327ZM16.6097 60.8467L16.1469 62.3894C16.0955 62.4923 16.0955 62.6465 15.9926 62.8522C15.9412 63.0579 15.8898 63.2122 15.8383 63.3151C16.0954 63.1094 16.3526 62.9037 16.6611 62.6465L17.8953 61.7724L16.6097 60.8467Z"
        fill={textColor}
      />
      <path
        d="M11.9817 53.4417L12.7017 54.2644L11.2104 59.201L11.3647 59.0468C11.6732 58.6868 11.9817 58.4297 12.2903 58.1726L14.4501 56.2699L14.9643 56.887L10.8504 60.538L10.1305 59.7153L11.6218 54.7787C11.5704 54.8301 11.4161 54.9843 11.159 55.2415C10.9019 55.4986 10.6962 55.7043 10.5419 55.8071L8.38212 57.7098L7.81647 57.0927L11.9817 53.4417Z"
        fill={textColor}
      />
      <path
        d="M5.39941 53.5445L5.45084 53.4417C5.96507 53.2874 6.58215 53.1331 7.30207 53.0303L7.66203 53.5445C6.94211 53.8016 6.37645 54.0073 5.86222 54.2644L5.39941 53.5445Z"
        fill={textColor}
      />
      <path
        d="M8.69063 48.2993L9.10202 49.1221L4.8339 51.179L5.55382 52.6189L4.88532 52.9788L3.08551 49.2764L3.75401 48.9164L4.47394 50.3562L8.69063 48.2993Z"
        fill={textColor}
      />
      <path
        d="M5.65654 38.4262L5.81081 39.4032L2.72543 40.843C2.57116 40.8945 2.41689 40.9973 2.15977 41.1002C1.90266 41.203 1.69696 41.2544 1.59412 41.3059C1.74839 41.3059 1.95408 41.3059 2.21119 41.3573C2.46831 41.3573 2.674 41.4087 2.77685 41.4087L6.17078 41.8201L6.32505 42.7971L3.70247 43.8256L1.07989 44.9569L0.925616 44.0313L3.95958 42.7971C4.47381 42.5914 4.8852 42.4372 5.24516 42.3343C5.03947 42.3343 4.78235 42.3343 4.52524 42.3343C4.26812 42.3343 4.06243 42.2829 3.85674 42.2829L0.617078 41.8201L0.462808 40.9459L3.49677 39.5575C3.75389 39.4032 4.2167 39.2489 4.78235 39.0947C4.42239 39.0947 3.95958 39.0947 3.39393 39.0432L0.102846 38.7347L0 37.8091L5.65654 38.4262Z"
        fill={textColor}
      />
      <path
        d="M5.81097 30.9184L4.21685 31.2784L3.95973 33.3868L5.39958 34.1067L5.29674 35.0323L0.0515747 32.3583L0.154421 31.3813L5.86239 29.9414L5.81097 30.9184ZM3.4455 31.4327L1.90281 31.7926C1.79996 31.8441 1.64569 31.8441 1.38858 31.8955C1.18288 31.9469 1.02861 31.9469 0.925768 31.9983C1.23431 32.1012 1.54285 32.2554 1.90281 32.4097L3.29123 33.0782L3.4455 31.4327Z"
        fill={textColor}
      />
      <path
        d="M6.32512 28.1929L0.977112 26.7531L1.23423 25.8789L6.53081 27.3188L6.32512 28.1929Z"
        fill={textColor}
      />
      <path
        d="M7.66211 24.0276L7.30215 24.8504L2.93118 23.0506L2.3141 24.5418L1.59418 24.2333L3.18829 20.428L3.90822 20.7365L3.29114 22.2278L7.66211 24.0276Z"
        fill={textColor}
      />
    </svg>
  );
}
