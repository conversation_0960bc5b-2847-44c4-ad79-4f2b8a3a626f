'use client';

import { useEffect } from 'react';

export default function ClickFix() {
  useEffect(() => {
    // Neutralize event blocking at the prototype level
    const originalStop = Event.prototype.stopPropagation;
    const originalPrevent = Event.prototype.preventDefault;

    Event.prototype.stopPropagation = function () {
      // Allow propagation but keep original function available
      return;
    };

    Event.prototype.preventDefault = function () {
      // Allow default actions but keep original function available
      return;
    };

    // Cleanup function to restore original behavior if needed
    return () => {
      Event.prototype.stopPropagation = originalStop;
      Event.prototype.preventDefault = originalPrevent;
    };
  }, []);

  useEffect(() => {
    // Remove any overlay elements InMobi might add
    const observer = new MutationObserver(mutations => {
      mutations.forEach(mutation => {
        mutation.addedNodes.forEach(node => {
          if (node instanceof HTMLElement && node.id === 'inmobi-overlay') {
            node.remove();
          }
        });
      });
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true,
    });

    return () => observer.disconnect();
  }, []);

  return null;
}
