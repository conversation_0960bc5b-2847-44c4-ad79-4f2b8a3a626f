import RightArrowIcon from '@components/Icons/RightArrowIcon';
import ComponentGrid from '@stories/ComponentGrid';
import ComponentTile from '@stories/ComponentTile';
import variableStyles from '@styles/variables.module.css';

import Button from './Button';

export default {
  title: 'Components/Utility/Button',
};

export function ButtonStory() {
  return (
    <div
      style={{
        padding: variableStyles.fiveSpace,
      }}
    >
      <ComponentGrid countColumns={3}>
        <ComponentTile label="Normal Button">
          <Button label="Get In Touch" type="button" />
        </ComponentTile>

        <ComponentTile label="Button with Right Icon">
          <Button
            label="Get In Touch"
            type="button"
            rightIcon={<RightArrowIcon />}
          />
        </ComponentTile>

        <ComponentTile label="Button with Left Icon">
          <Button
            label="Get In Touch"
            type="button"
            leftIcon={<RightArrowIcon />}
          />
        </ComponentTile>

        <ComponentTile label="Button with Link">
          <Button
            label="Get In Touch"
            type="button"
            isLink
            isExternal
            href="https://www.google.com"
          />
        </ComponentTile>
      </ComponentGrid>
    </div>
  );
}
